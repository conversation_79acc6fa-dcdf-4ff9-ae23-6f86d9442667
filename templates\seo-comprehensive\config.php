<?php
/**
 * SEO Comprehensive Template Configuration (Static)
 */

return array(
    'id' => 'seo-comprehensive',
    'name' => 'SEO Comprehensive',
    'description' => 'Template SEO-optimized dengan 13+ section untuk ranking maksimal (1500+ kata)',
    'sections' => array(
        'hero' => array(
            'name' => 'Hero Section',
            'fields' => array(
                'title' => ['type' => 'text', 'label' => 'Main Title (H1)', 'required' => true],
                'subtitle' => ['type' => 'textarea', 'label' => 'Subtitle'],
                'cta_text' => ['type' => 'text', 'label' => 'CTA Button Text'],
                'cta_link' => ['type' => 'url', 'label' => 'CTA Button Link'],
            )
        ),
        'benefits' => array(
            'name' => 'Benefits/Features (6 Benefits)',
            'fields' => array(
                'benefit_1_title' => ['type' => 'text', 'label' => 'Benefit 1 Title'],
                'benefit_1_desc' => ['type' => 'textarea', 'label' => 'Benefit 1 Description'],
                'benefit_2_title' => ['type' => 'text', 'label' => 'Benefit 2 Title'],
                'benefit_2_desc' => ['type' => 'textarea', 'label' => 'Benefit 2 Description'],
                'benefit_3_title' => ['type' => 'text', 'label' => 'Benefit 3 Title'],
                'benefit_3_desc' => ['type' => 'textarea', 'label' => 'Benefit 3 Description'],
                'benefit_4_title' => ['type' => 'text', 'label' => 'Benefit 4 Title'],
                'benefit_4_desc' => ['type' => 'textarea', 'label' => 'Benefit 4 Description'],
                'benefit_5_title' => ['type' => 'text', 'label' => 'Benefit 5 Title'],
                'benefit_5_desc' => ['type' => 'textarea', 'label' => 'Benefit 5 Description'],
                'benefit_6_title' => ['type' => 'text', 'label' => 'Benefit 6 Title'],
                'benefit_6_desc' => ['type' => 'textarea', 'label' => 'Benefit 6 Description'],
            )
        ),
        'testimonials' => array(
            'name' => 'Testimonials (6 Testimonials)',
            'fields' => array(
                'testimonial_1_name' => ['type' => 'text', 'label' => 'Testimonial 1 Name'],
                'testimonial_1_text' => ['type' => 'textarea', 'label' => 'Testimonial 1 Text'],
                'testimonial_2_name' => ['type' => 'text', 'label' => 'Testimonial 2 Name'],
                'testimonial_2_text' => ['type' => 'textarea', 'label' => 'Testimonial 2 Text'],
                'testimonial_3_name' => ['type' => 'text', 'label' => 'Testimonial 3 Name'],
                'testimonial_3_text' => ['type' => 'textarea', 'label' => 'Testimonial 3 Text'],
                'testimonial_4_name' => ['type' => 'text', 'label' => 'Testimonial 4 Name'],
                'testimonial_4_text' => ['type' => 'textarea', 'label' => 'Testimonial 4 Text'],
                'testimonial_5_name' => ['type' => 'text', 'label' => 'Testimonial 5 Name'],
                'testimonial_5_text' => ['type' => 'textarea', 'label' => 'Testimonial 5 Text'],
                'testimonial_6_name' => ['type' => 'text', 'label' => 'Testimonial 6 Name'],
                'testimonial_6_text' => ['type' => 'textarea', 'label' => 'Testimonial 6 Text'],
            )
        ),
        'services' => array(
            'name' => 'Services/Pricing (3 Plans)',
            'fields' => array(
                'plan_1_title' => ['type' => 'text', 'label' => 'Plan 1 Title'],
                'plan_1_price' => ['type' => 'text', 'label' => 'Plan 1 Price'],
                'plan_1_features' => ['type' => 'textarea', 'label' => 'Plan 1 Features (one per line)'],
                'plan_2_title' => ['type' => 'text', 'label' => 'Plan 2 Title'],
                'plan_2_price' => ['type' => 'text', 'label' => 'Plan 2 Price'],
                'plan_2_features' => ['type' => 'textarea', 'label' => 'Plan 2 Features (one per line)'],
                'plan_3_title' => ['type' => 'text', 'label' => 'Plan 3 Title'],
                'plan_3_price' => ['type' => 'text', 'label' => 'Plan 3 Price'],
                'plan_3_features' => ['type' => 'textarea', 'label' => 'Plan 3 Features (one per line)'],
            )
        ),
        'process' => array(
            'name' => 'Process/Workflow (6 Steps)',
            'fields' => array(
                'process_title' => ['type' => 'text', 'label' => 'Process Section Title'],
                'process_subtitle' => ['type' => 'textarea', 'label' => 'Process Section Subtitle'],
                'process_1_title' => ['type' => 'text', 'label' => 'Step 1 Title'],
                'process_1_description' => ['type' => 'textarea', 'label' => 'Step 1 Description'],
                'process_2_title' => ['type' => 'text', 'label' => 'Step 2 Title'],
                'process_2_description' => ['type' => 'textarea', 'label' => 'Step 2 Description'],
                'process_3_title' => ['type' => 'text', 'label' => 'Step 3 Title'],
                'process_3_description' => ['type' => 'textarea', 'label' => 'Step 3 Description'],
                'process_4_title' => ['type' => 'text', 'label' => 'Step 4 Title'],
                'process_4_description' => ['type' => 'textarea', 'label' => 'Step 4 Description'],
                'process_5_title' => ['type' => 'text', 'label' => 'Step 5 Title'],
                'process_5_description' => ['type' => 'textarea', 'label' => 'Step 5 Description'],
                'process_6_title' => ['type' => 'text', 'label' => 'Step 6 Title'],
                'process_6_description' => ['type' => 'textarea', 'label' => 'Step 6 Description'],
            )
        ),
        'pricing' => array(
            'name' => 'Pricing Plans (3 Detailed Plans)',
            'fields' => array(
                'pricing_title' => ['type' => 'text', 'label' => 'Pricing Section Title'],
                'pricing_subtitle' => ['type' => 'textarea', 'label' => 'Pricing Section Subtitle'],
                'pricing_1_name' => ['type' => 'text', 'label' => 'Plan 1 Name'],
                'pricing_1_price' => ['type' => 'text', 'label' => 'Plan 1 Price'],
                'pricing_1_period' => ['type' => 'text', 'label' => 'Plan 1 Period'],
                'pricing_1_feature_1' => ['type' => 'text', 'label' => 'Plan 1 Feature 1'],
                'pricing_1_feature_2' => ['type' => 'text', 'label' => 'Plan 1 Feature 2'],
                'pricing_1_feature_3' => ['type' => 'text', 'label' => 'Plan 1 Feature 3'],
                'pricing_1_feature_4' => ['type' => 'text', 'label' => 'Plan 1 Feature 4'],
                'pricing_1_feature_5' => ['type' => 'text', 'label' => 'Plan 1 Feature 5'],
                'pricing_1_feature_6' => ['type' => 'text', 'label' => 'Plan 1 Feature 6'],
                'pricing_1_cta_text' => ['type' => 'text', 'label' => 'Plan 1 CTA Text'],
                'pricing_1_cta_link' => ['type' => 'url', 'label' => 'Plan 1 CTA Link'],
                'pricing_2_name' => ['type' => 'text', 'label' => 'Plan 2 Name'],
                'pricing_2_badge' => ['type' => 'text', 'label' => 'Plan 2 Badge (e.g., MOST POPULAR)'],
                'pricing_2_price' => ['type' => 'text', 'label' => 'Plan 2 Price'],
                'pricing_2_period' => ['type' => 'text', 'label' => 'Plan 2 Period'],
                'pricing_2_feature_1' => ['type' => 'text', 'label' => 'Plan 2 Feature 1'],
                'pricing_2_feature_2' => ['type' => 'text', 'label' => 'Plan 2 Feature 2'],
                'pricing_2_feature_3' => ['type' => 'text', 'label' => 'Plan 2 Feature 3'],
                'pricing_2_feature_4' => ['type' => 'text', 'label' => 'Plan 2 Feature 4'],
                'pricing_2_feature_5' => ['type' => 'text', 'label' => 'Plan 2 Feature 5'],
                'pricing_2_feature_6' => ['type' => 'text', 'label' => 'Plan 2 Feature 6'],
                'pricing_2_feature_7' => ['type' => 'text', 'label' => 'Plan 2 Feature 7'],
                'pricing_2_feature_8' => ['type' => 'text', 'label' => 'Plan 2 Feature 8'],
                'pricing_2_cta_text' => ['type' => 'text', 'label' => 'Plan 2 CTA Text'],
                'pricing_2_cta_link' => ['type' => 'url', 'label' => 'Plan 2 CTA Link'],
                'pricing_3_name' => ['type' => 'text', 'label' => 'Plan 3 Name'],
                'pricing_3_price' => ['type' => 'text', 'label' => 'Plan 3 Price'],
                'pricing_3_period' => ['type' => 'text', 'label' => 'Plan 3 Period'],
                'pricing_3_feature_1' => ['type' => 'text', 'label' => 'Plan 3 Feature 1'],
                'pricing_3_feature_2' => ['type' => 'text', 'label' => 'Plan 3 Feature 2'],
                'pricing_3_feature_3' => ['type' => 'text', 'label' => 'Plan 3 Feature 3'],
                'pricing_3_feature_4' => ['type' => 'text', 'label' => 'Plan 3 Feature 4'],
                'pricing_3_feature_5' => ['type' => 'text', 'label' => 'Plan 3 Feature 5'],
                'pricing_3_feature_6' => ['type' => 'text', 'label' => 'Plan 3 Feature 6'],
                'pricing_3_feature_7' => ['type' => 'text', 'label' => 'Plan 3 Feature 7'],
                'pricing_3_feature_8' => ['type' => 'text', 'label' => 'Plan 3 Feature 8'],
                'pricing_3_feature_9' => ['type' => 'text', 'label' => 'Plan 3 Feature 9'],
                'pricing_3_cta_text' => ['type' => 'text', 'label' => 'Plan 3 CTA Text'],
                'pricing_3_cta_link' => ['type' => 'url', 'label' => 'Plan 3 CTA Link'],
                'pricing_consultation_text' => ['type' => 'text', 'label' => 'Consultation Text'],
                'pricing_consultation_cta' => ['type' => 'text', 'label' => 'Consultation CTA'],
                'pricing_consultation_link' => ['type' => 'url', 'label' => 'Consultation Link'],
            )
        ),
        'faq' => array(
            'name' => 'FAQ Section (10 Questions)',
            'fields' => array(
                'faq_1_question' => ['type' => 'text', 'label' => 'FAQ 1 Question'],
                'faq_1_answer' => ['type' => 'textarea', 'label' => 'FAQ 1 Answer'],
                'faq_2_question' => ['type' => 'text', 'label' => 'FAQ 2 Question'],
                'faq_2_answer' => ['type' => 'textarea', 'label' => 'FAQ 2 Answer'],
                'faq_3_question' => ['type' => 'text', 'label' => 'FAQ 3 Question'],
                'faq_3_answer' => ['type' => 'textarea', 'label' => 'FAQ 3 Answer'],
                'faq_4_question' => ['type' => 'text', 'label' => 'FAQ 4 Question'],
                'faq_4_answer' => ['type' => 'textarea', 'label' => 'FAQ 4 Answer'],
                'faq_5_question' => ['type' => 'text', 'label' => 'FAQ 5 Question'],
                'faq_5_answer' => ['type' => 'textarea', 'label' => 'FAQ 5 Answer'],
                'faq_6_question' => ['type' => 'text', 'label' => 'FAQ 6 Question'],
                'faq_6_answer' => ['type' => 'textarea', 'label' => 'FAQ 6 Answer'],
                'faq_7_question' => ['type' => 'text', 'label' => 'FAQ 7 Question'],
                'faq_7_answer' => ['type' => 'textarea', 'label' => 'FAQ 7 Answer'],
                'faq_8_question' => ['type' => 'text', 'label' => 'FAQ 8 Question'],
                'faq_8_answer' => ['type' => 'textarea', 'label' => 'FAQ 8 Answer'],
                'faq_9_question' => ['type' => 'text', 'label' => 'FAQ 9 Question'],
                'faq_9_answer' => ['type' => 'textarea', 'label' => 'FAQ 9 Answer'],
                'faq_10_question' => ['type' => 'text', 'label' => 'FAQ 10 Question'],
                'faq_10_answer' => ['type' => 'textarea', 'label' => 'FAQ 10 Answer'],
            )
        ),
        'case_studies' => array(
            'name' => 'Case Studies (3 Studies)',
            'fields' => array(
                'study_1_title' => ['type' => 'text', 'label' => 'Study 1 Title'],
                'study_1_desc' => ['type' => 'textarea', 'label' => 'Study 1 Description'],
                'study_2_title' => ['type' => 'text', 'label' => 'Study 2 Title'],
                'study_2_desc' => ['type' => 'textarea', 'label' => 'Study 2 Description'],
                'study_3_title' => ['type' => 'text', 'label' => 'Study 3 Title'],
                'study_3_desc' => ['type' => 'textarea', 'label' => 'Study 3 Description'],
            )
        ),
        'guarantees' => array(
            'name' => 'Guarantees & Trust Badges',
            'fields' => array(
                'guarantees_title' => ['type' => 'text', 'label' => 'Guarantees Section Title'],
                'guarantees_subtitle' => ['type' => 'textarea', 'label' => 'Guarantees Section Subtitle'],
                'guarantees_1_icon' => ['type' => 'text', 'label' => 'Guarantee 1 Icon (FontAwesome class)'],
                'guarantees_1_title' => ['type' => 'text', 'label' => 'Guarantee 1 Title'],
                'guarantee_1_item_1_title' => ['type' => 'text', 'label' => 'Guarantee 1 Item 1 Title'],
                'guarantee_1_item_1_desc' => ['type' => 'text', 'label' => 'Guarantee 1 Item 1 Description'],
                'guarantee_1_item_2_title' => ['type' => 'text', 'label' => 'Guarantee 1 Item 2 Title'],
                'guarantee_1_item_2_desc' => ['type' => 'text', 'label' => 'Guarantee 1 Item 2 Description'],
                'guarantee_1_item_3_title' => ['type' => 'text', 'label' => 'Guarantee 1 Item 3 Title'],
                'guarantee_1_item_3_desc' => ['type' => 'text', 'label' => 'Guarantee 1 Item 3 Description'],
                'guarantee_1_item_4_title' => ['type' => 'text', 'label' => 'Guarantee 1 Item 4 Title'],
                'guarantee_1_item_4_desc' => ['type' => 'text', 'label' => 'Guarantee 1 Item 4 Description'],
                'guarantee_1_item_5_title' => ['type' => 'text', 'label' => 'Guarantee 1 Item 5 Title'],
                'guarantee_1_item_5_desc' => ['type' => 'text', 'label' => 'Guarantee 1 Item 5 Description'],
                'guarantee_1_item_6_title' => ['type' => 'text', 'label' => 'Guarantee 1 Item 6 Title'],
                'guarantee_1_item_6_desc' => ['type' => 'text', 'label' => 'Guarantee 1 Item 6 Description'],
                'guarantees_2_icon' => ['type' => 'text', 'label' => 'Guarantee 2 Icon (FontAwesome class)'],
                'guarantees_2_title' => ['type' => 'text', 'label' => 'Guarantee 2 Title'],
                'guarantee_2_item_1_title' => ['type' => 'text', 'label' => 'Guarantee 2 Item 1 Title'],
                'guarantee_2_item_1_desc' => ['type' => 'text', 'label' => 'Guarantee 2 Item 1 Description'],
                'guarantee_2_item_2_title' => ['type' => 'text', 'label' => 'Guarantee 2 Item 2 Title'],
                'guarantee_2_item_2_desc' => ['type' => 'text', 'label' => 'Guarantee 2 Item 2 Description'],
                'guarantee_2_item_3_title' => ['type' => 'text', 'label' => 'Guarantee 2 Item 3 Title'],
                'guarantee_2_item_3_desc' => ['type' => 'text', 'label' => 'Guarantee 2 Item 3 Description'],
                'guarantee_2_item_4_title' => ['type' => 'text', 'label' => 'Guarantee 2 Item 4 Title'],
                'guarantee_2_item_4_desc' => ['type' => 'text', 'label' => 'Guarantee 2 Item 4 Description'],
                'guarantee_2_item_5_title' => ['type' => 'text', 'label' => 'Guarantee 2 Item 5 Title'],
                'guarantee_2_item_5_desc' => ['type' => 'text', 'label' => 'Guarantee 2 Item 5 Description'],
                'guarantee_2_item_6_title' => ['type' => 'text', 'label' => 'Guarantee 2 Item 6 Title'],
                'guarantee_2_item_6_desc' => ['type' => 'text', 'label' => 'Guarantee 2 Item 6 Description'],
            )
        ),
        'guide' => array(
            'name' => 'Guide Section (2 Categories)',
            'fields' => array(
                'guide_title' => ['type' => 'text', 'label' => 'Guide Section Title'],
                'guide_subtitle' => ['type' => 'textarea', 'label' => 'Guide Section Subtitle'],
                'guide_1_icon' => ['type' => 'text', 'label' => 'Guide 1 Icon (FontAwesome class)'],
                'guide_1_title' => ['type' => 'text', 'label' => 'Guide 1 Title'],
                'guide_1_item_1_title' => ['type' => 'text', 'label' => 'Guide 1 Item 1 Title'],
                'guide_1_item_1_desc' => ['type' => 'text', 'label' => 'Guide 1 Item 1 Description'],
                'guide_1_item_2_title' => ['type' => 'text', 'label' => 'Guide 1 Item 2 Title'],
                'guide_1_item_2_desc' => ['type' => 'text', 'label' => 'Guide 1 Item 2 Description'],
                'guide_1_item_3_title' => ['type' => 'text', 'label' => 'Guide 1 Item 3 Title'],
                'guide_1_item_3_desc' => ['type' => 'text', 'label' => 'Guide 1 Item 3 Description'],
                'guide_1_item_4_title' => ['type' => 'text', 'label' => 'Guide 1 Item 4 Title'],
                'guide_1_item_4_desc' => ['type' => 'text', 'label' => 'Guide 1 Item 4 Description'],
                'guide_1_item_5_title' => ['type' => 'text', 'label' => 'Guide 1 Item 5 Title'],
                'guide_1_item_5_desc' => ['type' => 'text', 'label' => 'Guide 1 Item 5 Description'],
                'guide_2_icon' => ['type' => 'text', 'label' => 'Guide 2 Icon (FontAwesome class)'],
                'guide_2_title' => ['type' => 'text', 'label' => 'Guide 2 Title'],
                'guide_2_item_1_title' => ['type' => 'text', 'label' => 'Guide 2 Item 1 Title'],
                'guide_2_item_1_desc' => ['type' => 'text', 'label' => 'Guide 2 Item 1 Description'],
                'guide_2_item_2_title' => ['type' => 'text', 'label' => 'Guide 2 Item 2 Title'],
                'guide_2_item_2_desc' => ['type' => 'text', 'label' => 'Guide 2 Item 2 Description'],
                'guide_2_item_3_title' => ['type' => 'text', 'label' => 'Guide 2 Item 3 Title'],
                'guide_2_item_3_desc' => ['type' => 'text', 'label' => 'Guide 2 Item 3 Description'],
                'guide_2_item_4_title' => ['type' => 'text', 'label' => 'Guide 2 Item 4 Title'],
                'guide_2_item_4_desc' => ['type' => 'text', 'label' => 'Guide 2 Item 4 Description'],
                'guide_2_item_5_title' => ['type' => 'text', 'label' => 'Guide 2 Item 5 Title'],
                'guide_2_item_5_desc' => ['type' => 'text', 'label' => 'Guide 2 Item 5 Description'],
                'guide_cta_text' => ['type' => 'text', 'label' => 'Guide CTA Text'],
                'guide_cta_description' => ['type' => 'text', 'label' => 'Guide CTA Description'],
                'guide_cta_button' => ['type' => 'text', 'label' => 'Guide CTA Button Text'],
                'guide_cta_icon' => ['type' => 'text', 'label' => 'Guide CTA Icon (FontAwesome class)'],
                'guide_cta_link' => ['type' => 'url', 'label' => 'Guide CTA Link'],
            )
        ),
        'locations' => array(
            'name' => 'Locations/Service Areas (3 Locations)',
            'fields' => array(
                'locations_title' => ['type' => 'text', 'label' => 'Locations Section Title'],
                'locations_subtitle' => ['type' => 'textarea', 'label' => 'Locations Section Subtitle'],
                'location_1_icon' => ['type' => 'text', 'label' => 'Location 1 Icon (FontAwesome class)'],
                'location_1_title' => ['type' => 'text', 'label' => 'Location 1 Title'],
                'location_1_description' => ['type' => 'textarea', 'label' => 'Location 1 Description'],
                'location_1_area_1' => ['type' => 'text', 'label' => 'Location 1 Area 1'],
                'location_1_area_2' => ['type' => 'text', 'label' => 'Location 1 Area 2'],
                'location_1_area_3' => ['type' => 'text', 'label' => 'Location 1 Area 3'],
                'location_1_area_4' => ['type' => 'text', 'label' => 'Location 1 Area 4'],
                'location_2_icon' => ['type' => 'text', 'label' => 'Location 2 Icon (FontAwesome class)'],
                'location_2_title' => ['type' => 'text', 'label' => 'Location 2 Title'],
                'location_2_description' => ['type' => 'textarea', 'label' => 'Location 2 Description'],
                'location_2_area_1' => ['type' => 'text', 'label' => 'Location 2 Area 1'],
                'location_2_area_2' => ['type' => 'text', 'label' => 'Location 2 Area 2'],
                'location_2_area_3' => ['type' => 'text', 'label' => 'Location 2 Area 3'],
                'location_2_area_4' => ['type' => 'text', 'label' => 'Location 2 Area 4'],
                'location_3_icon' => ['type' => 'text', 'label' => 'Location 3 Icon (FontAwesome class)'],
                'location_3_title' => ['type' => 'text', 'label' => 'Location 3 Title'],
                'location_3_description' => ['type' => 'textarea', 'label' => 'Location 3 Description'],
                'location_3_area_1' => ['type' => 'text', 'label' => 'Location 3 Area 1'],
                'location_3_area_2' => ['type' => 'text', 'label' => 'Location 3 Area 2'],
                'location_3_area_3' => ['type' => 'text', 'label' => 'Location 3 Area 3'],
                'location_3_area_4' => ['type' => 'text', 'label' => 'Location 3 Area 4'],
            )
        ),
        'about' => array(
            'name' => 'About Section',
            'fields' => array(
                'about_title' => ['type' => 'text', 'label' => 'About Title'],
                'about_subtitle' => ['type' => 'textarea', 'label' => 'About Subtitle'],
                'about_description' => ['type' => 'textarea', 'label' => 'About Description'],
                'about_stats_1_number' => ['type' => 'text', 'label' => 'Stat 1 Number'],
                'about_stats_1_label' => ['type' => 'text', 'label' => 'Stat 1 Label'],
                'about_stats_2_number' => ['type' => 'text', 'label' => 'Stat 2 Number'],
                'about_stats_2_label' => ['type' => 'text', 'label' => 'Stat 2 Label'],
                'about_stats_3_number' => ['type' => 'text', 'label' => 'Stat 3 Number'],
                'about_stats_3_label' => ['type' => 'text', 'label' => 'Stat 3 Label'],
                'about_stats_4_number' => ['type' => 'text', 'label' => 'Stat 4 Number'],
                'about_stats_4_label' => ['type' => 'text', 'label' => 'Stat 4 Label'],
            )
        ),
        'contact' => array(
            'name' => 'Contact Information',
            'fields' => array(
                'contact_email' => ['type' => 'email', 'label' => 'Contact Email'],
                'contact_phone' => ['type' => 'tel', 'label' => 'Contact Phone'],
                'contact_address' => ['type' => 'text', 'label' => 'Contact Address'],
            )
        ),
        'final_cta' => array(
            'name' => 'Final CTA Section',
            'fields' => array(
                'final_cta_title' => ['type' => 'text', 'label' => 'Final CTA Title'],
                'final_cta_subtitle' => ['type' => 'textarea', 'label' => 'Final CTA Subtitle'],
                'final_cta_button_text' => ['type' => 'text', 'label' => 'Final CTA Button Text'],
                'final_cta_button_link' => ['type' => 'url', 'label' => 'Final CTA Button Link'],
            )
        ),
    ),
    'style_options' => array(
        'typography' => array(
            'heading_font' => array(
                'label' => 'Heading Font',
                'type' => 'select',
                'default' => 'Inter',
                'options' => [
                    'Inter' => 'Inter',
                    'Roboto' => 'Roboto',
                    'Open Sans' => 'Open Sans',
                    'Lato' => 'Lato',
                    'Montserrat' => 'Montserrat',
                ]
            ),
            'body_font' => array(
                'label' => 'Body Font',
                'type' => 'select',
                'default' => 'Inter',
                'options' => [
                    'Inter' => 'Inter',
                    'Roboto' => 'Roboto',
                    'Open Sans' => 'Open Sans',
                    'Lato' => 'Lato',
                    'Montserrat' => 'Montserrat',
                ]
            )
        )
    )
);
