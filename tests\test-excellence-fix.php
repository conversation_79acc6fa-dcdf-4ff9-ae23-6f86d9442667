<?php
define('RIFE_PG_TESTING', true);
require_once dirname(__FILE__) . '/../modules/class-image-generator.php';

echo "Testing Excellence Fix...\n";

$gen = new Rife_PG_Image_Generator();
$result = $gen->generate_image('Test Image', 'jasa seo jakarta');

echo "Generated ALT text: " . $result['alt_text'] . "\n";
echo "Contains 'excellence': " . (stripos($result['alt_text'], 'excellence') !== false ? 'YES' : 'NO') . "\n";

if (file_exists($result['path'])) {
    unlink($result['path']);
    echo "Test file cleaned up\n";
}

echo "Test completed\n";
?>
