<?php
echo "Debug Test Starting...\n";

// Define testing mode
define('RIFE_PG_TESTING', true);

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "PHP Version: " . phpversion() . "\n";
echo "GD Extension: " . (extension_loaded('gd') ? 'Available' : 'Not Available') . "\n";

echo "Attempting to load image generator class...\n";

try {
    $file_path = dirname(__FILE__) . '/../modules/class-image-generator.php';
    echo "File path: $file_path\n";
    echo "File exists: " . (file_exists($file_path) ? 'YES' : 'NO') . "\n";
    
    if (file_exists($file_path)) {
        echo "File size: " . filesize($file_path) . " bytes\n";
        echo "Including file...\n";
        
        require_once $file_path;
        
        echo "File included successfully\n";
        echo "Class exists: " . (class_exists('Rife_PG_Image_Generator') ? 'YES' : 'NO') . "\n";
        
        if (class_exists('Rife_PG_Image_Generator')) {
            echo "Attempting to create instance...\n";
            $generator = new Rife_PG_Image_Generator();
            echo "Instance created successfully!\n";
            
            echo "Testing simple image generation...\n";
            $result = $generator->generate_image('Simple Test', 'test');
            
            if (is_array($result)) {
                echo "Result received: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
                if (!$result['success']) {
                    echo "Error: " . ($result['error'] ?? 'Unknown') . "\n";
                } else {
                    echo "Generated file: " . $result['filename'] . "\n";
                    if (isset($result['path']) && file_exists($result['path'])) {
                        echo "File created successfully, size: " . filesize($result['path']) . " bytes\n";
                        unlink($result['path']); // Cleanup
                        echo "Test file cleaned up\n";
                    }
                }
            } else {
                echo "Invalid result format\n";
            }
        }
    }
    
} catch (ParseError $e) {
    echo "PARSE ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "FATAL ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
} catch (Exception $e) {
    echo "EXCEPTION: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "Debug test completed.\n";
?>
