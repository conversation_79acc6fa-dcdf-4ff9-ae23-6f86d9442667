<?php
/**
 * Admin functionality for Rife PageGenerator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rife_PG_Admin {
    
    /**
     * Menu title
     */
    private $menu_title = 'Rife PageGenerator';
    
    /**
     * Submenu titles
     */
    private $submenu_titles = array(
        'gallery' => 'Template Gallery',
        'generator' => 'Page Generator',
        'pages' => 'Generated Pages',
        'settings' => 'Settings'
    );
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize translations
     */
    public function init() {
        // Initialize menu and submenu titles with proper translation
        $this->menu_title = 'Rife PageGenerator';
        $this->submenu_titles = array(
            'gallery' => 'Template Gallery',
            'generator' => 'Page Generator',
            'pages' => 'Generated Pages',
            'settings' => 'Settings'
        );
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Main menu
        add_menu_page(
            $this->menu_title,
            'PageGenerator',
            'manage_options',
            'rife-pg-gallery', // Changed from rife-pagegenerator
            array($this, 'template_gallery_page'),
            'dashicons-layout',
            30
        );
        
        // Template Gallery submenu
        add_submenu_page(
            'rife-pg-gallery', // Changed from rife-pagegenerator
            $this->submenu_titles['gallery'],
            $this->submenu_titles['gallery'],
            'manage_options',
            'rife-pg-gallery', // Changed from rife-pagegenerator
            array($this, 'template_gallery_page')
        );
        
        // Page Generator submenu
        add_submenu_page(
            'rife-pg-gallery', // Changed from rife-pagegenerator
            $this->submenu_titles['generator'],
            $this->submenu_titles['generator'],
            'manage_options',
            'rife-pg-generator',
            array($this, 'page_generator_page')
        );
        
        // Generated Pages submenu
        add_submenu_page(
            'rife-pg-gallery', // Changed from rife-pagegenerator
            $this->submenu_titles['pages'],
            $this->submenu_titles['pages'],
            'manage_options',
            'rife-pg-pages',
            array($this, 'generated_pages_page')
        );
        
        // Settings submenu
        add_submenu_page(
            'rife-pg-gallery', // Changed from rife-pagegenerator
            $this->submenu_titles['settings'],
            $this->submenu_titles['settings'],
            'manage_options',
            'rife-pg-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'rife-') === false) {
            return;
        }
        
        // Enqueue WordPress color picker
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
        
        // Enqueue admin styles
        wp_enqueue_style(
            'rife-pg-admin',
            RIFE_PG_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            time() // RIFE_PG_VERSION
        );
        
        // Enqueue admin scripts
        wp_enqueue_script(
            'rife-pg-admin',
            RIFE_PG_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery', 'wp-color-picker'),
            time(), // RIFE_PG_VERSION
            true
        );
        
        // Localize script
        wp_localize_script('rife-pg-admin', 'rifePgAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('rife_pg_generate'),
            'selectedTemplate' => isset($_GET['template']) ? sanitize_text_field($_GET['template']) : '',
            'strings' => array(
                'generating' => 'Generating page...',
                'success' => 'Page generated successfully!',
                'error' => 'Error generating page. Please try again.',
                'preview' => 'Preview',
                'close' => 'Close'
            )
        ));
    }
    
    /**
     * Template Gallery page
     */
    public function template_gallery_page() {
        $template_manager = rife_pg()->template_manager;
        $templates = $template_manager->get_available_templates();
        $categories = $template_manager->get_template_categories();
        
        include RIFE_PG_PLUGIN_DIR . 'admin/views/template-gallery.php';
    }
    
    /**
     * Page Generator page
     */
    public function page_generator_page() {
        // Ensure templates are loaded after init hook
        $template_manager = rife_pg()->template_manager;
        $templates = $template_manager->get_available_templates();
        
        // Get selected template from URL parameter
        $selected_template = isset($_GET['template']) ? sanitize_text_field($_GET['template']) : '';
        
        include RIFE_PG_PLUGIN_DIR . 'admin/views/page-generator.php';
    }
    
    /**
     * Generated Pages page
     */
    public function generated_pages_page() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'rife_pg_pages';
        $generated_pages = $wpdb->get_results(
            "SELECT * FROM $table_name ORDER BY created_at DESC"
        );
        
        include RIFE_PG_PLUGIN_DIR . 'admin/views/generated-pages.php';
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['_wpnonce'], 'rife_pg_settings')) {
            $this->save_settings();
        }
        
        $options = get_option('rife_pg_options', array());
        
        include RIFE_PG_PLUGIN_DIR . 'admin/views/settings.php';
    }
    
    /**
     * Save settings
     */
    private function save_settings() {
        $options = array(
            'default_template' => sanitize_text_field($_POST['default_template']),
            'enable_preview' => isset($_POST['enable_preview']),
            'auto_publish' => isset($_POST['auto_publish']),
            'default_colors' => array(
                'primary' => sanitize_hex_color($_POST['primary_color']),
                'secondary' => sanitize_hex_color($_POST['secondary_color'])
            ),
            'default_fonts' => array(
                'heading' => sanitize_text_field($_POST['heading_font']),
                'body' => sanitize_text_field($_POST['body_font'])
            )
        );
        
        update_option('rife_pg_options', $options);
        
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' . 
                 'Settings saved successfully!' . '</p></div>';
        });
    }
}