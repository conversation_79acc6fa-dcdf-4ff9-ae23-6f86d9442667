<?php
/**
 * Test Suite for Rife PageGenerator
 */

class Test_Rife_PageGenerator extends WP_UnitTestCase {
    
    private $plugin;
    
    public function setUp() {
        parent::setUp();
        $this->plugin = rife_pg();
    }
    
    /**
     * Test plugin initialization
     */
    public function test_plugin_initialization() {
        $this->assertInstanceOf('Rife_PageGenerator', $this->plugin);
        $this->assertInstanceOf('Rife_PG_Template_Manager', $this->plugin->template_manager);
        $this->assertInstanceOf('Rife_PG_Page_Generator', $this->plugin->page_generator);
        $this->assertInstanceOf('Rife_PG_Database', $this->plugin->database);
    }
    
    /**
     * Test database table creation
     */
    public function test_database_tables() {
        global $wpdb;
        
        $this->plugin->database->create_tables();
        
        $tables = array(
            $wpdb->prefix . 'rife_pg_pages',
            $wpdb->prefix . 'rife_pg_templates',
            $wpdb->prefix . 'rife_pg_styles',
            $wpdb->prefix . 'rife_pg_analytics'
        );
        
        foreach ($tables as $table) {
            $this->assertEquals($table, $wpdb->get_var("SHOW TABLES LIKE '$table'"));
        }
    }
    
    /**
     * Test template manager
     */
    public function test_template_manager() {
        $templates = $this->plugin->template_manager->get_available_templates();
        
        $this->assertIsArray($templates);
        $this->assertGreaterThan(0, count($templates));
        
        // Test specific template
        $template = $this->plugin->template_manager->get_template('business-basic');
        $this->assertIsArray($template);
        $this->assertEquals('business-basic', $template['id']);
        $this->assertEquals('Business Basic', $template['name']);
    }
    
    /**
     * Test page generation
     */
    public function test_page_generation() {
        $template_id = 'business-basic';
        $content_data = array(
            'hero' => array(
                'title' => 'Test Page Title',
                'subtitle' => 'Test page subtitle',
                'cta_text' => 'Get Started',
                'cta_link' => '#'
            )
        );
        $style_data = array(
            'primary_color' => '#007cba',
            'secondary_color' => '#50575e'
        );
        
        $result = $this->plugin->page_generator->generate_page($template_id, $content_data, $style_data);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('page_id', $result);
        $this->assertArrayHasKey('page_url', $result);
        
        // Verify page was created
        $page = get_post($result['page_id']);
        $this->assertInstanceOf('WP_Post', $page);
        $this->assertEquals('Test Page Title', $page->post_title);
    }
    
    /**
     * Test style customizer
     */
    public function test_style_customizer() {
        $style_customizer = new Rife_PG_Style_Customizer();
        
        $fonts = $style_customizer->get_available_fonts();
        $this->assertIsArray($fonts);
        $this->assertArrayHasKey('Inter', $fonts);
        
        $style_data = array(
            'primary_color' => '#007cba',
            'heading_font' => 'Inter',
            'font_size_base' => '16'
        );
        
        $css = $style_customizer->generate_css($style_data);
        $this->assertIsString($css);
        $this->assertStringContains('--primary-color: #007cba', $css);
    }
    
    /**
     * Test AJAX handlers
     */
    public function test_ajax_handlers() {
        $ajax_handler = new Rife_PG_Ajax_Handler();
        $this->assertInstanceOf('Rife_PG_Ajax_Handler', $ajax_handler);
        
        // Test that AJAX actions are registered
        $this->assertTrue(has_action('wp_ajax_rife_pg_preview_template'));
        $this->assertTrue(has_action('wp_ajax_rife_pg_generate_page'));
        $this->assertTrue(has_action('wp_ajax_rife_pg_delete_page'));
    }
    
    /**
     * Test data sanitization
     */
    public function test_data_sanitization() {
        $content_data = array(
            'hero' => array(
                'title' => '<script>alert("xss")</script>Test Title',
                'subtitle' => 'Test subtitle with <b>HTML</b>',
                'cta_text' => 'Click Here'
            )
        );
        
        $ajax_handler = new Rife_PG_Ajax_Handler();
        $reflection = new ReflectionClass($ajax_handler);
        $method = $reflection->getMethod('sanitize_content_data');
        $method->setAccessible(true);
        
        $sanitized = $method->invoke($ajax_handler, $content_data);
        
        $this->assertStringNotContains('<script>', $sanitized['hero']['title']);
        $this->assertEquals('Test Title', $sanitized['hero']['title']);
    }
    
    /**
     * Test template validation
     */
    public function test_template_validation() {
        $template_id = 'business-basic';
        $valid_data = array(
            'hero' => array(
                'title' => 'Valid Title',
                'subtitle' => 'Valid subtitle'
            )
        );
        
        $invalid_data = array(
            'hero' => array(
                'title' => '' // Empty required field
            )
        );
        
        $result_valid = $this->plugin->template_manager->validate_template_data($template_id, $valid_data);
        $result_invalid = $this->plugin->template_manager->validate_template_data($template_id, $invalid_data);
        
        $this->assertTrue($result_valid);
        $this->assertIsArray($result_invalid); // Should return array of errors
    }
    
    /**
     * Test page deletion
     */
    public function test_page_deletion() {
        // First create a page
        $template_id = 'business-basic';
        $content_data = array(
            'hero' => array(
                'title' => 'Test Delete Page',
                'subtitle' => 'This page will be deleted'
            )
        );
        
        $result = $this->plugin->page_generator->generate_page($template_id, $content_data, array());
        $page_id = $result['page_id'];
        
        // Now delete it
        $delete_result = $this->plugin->page_generator->delete_page($page_id);
        
        $this->assertTrue($delete_result['success']);
        
        // Verify page is deleted
        $page = get_post($page_id);
        $this->assertNull($page);
    }
    
    /**
     * Test database cleanup
     */
    public function test_database_cleanup() {
        // Create some test data
        global $wpdb;
        $analytics_table = $this->plugin->database->get_table('analytics');
        
        $old_date = date('Y-m-d H:i:s', strtotime('-35 days'));
        $wpdb->insert($analytics_table, array(
            'page_id' => 1,
            'template_id' => 'test',
            'event_type' => 'view',
            'created_at' => $old_date
        ));
        
        // Run cleanup
        $this->plugin->database->cleanup_old_data(30);
        
        // Verify old data is removed
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $analytics_table WHERE created_at < %s",
            date('Y-m-d H:i:s', strtotime('-30 days'))
        ));
        
        $this->assertEquals(0, $count);
    }
    
    /**
     * Test plugin deactivation cleanup
     */
    public function test_plugin_deactivation() {
        // This would test cleanup on deactivation
        // For now, just verify the method exists
        $this->assertTrue(method_exists($this->plugin, 'deactivate'));
    }
    
    /**
     * Cleanup after tests
     */
    public function tearDown() {
        // Clean up any test data
        global $wpdb;
        
        $tables = array(
            $wpdb->prefix . 'rife_pg_pages',
            $wpdb->prefix . 'rife_pg_templates',
            $wpdb->prefix . 'rife_pg_styles',
            $wpdb->prefix . 'rife_pg_analytics'
        );
        
        foreach ($tables as $table) {
            $wpdb->query("DELETE FROM $table WHERE 1=1");
        }
        
        parent::tearDown();
    }
}

/**
 * Test Template Manager specifically
 */
class Test_Template_Manager extends WP_UnitTestCase {
    
    private $template_manager;
    
    public function setUp() {
        parent::setUp();
        $this->template_manager = new Rife_PG_Template_Manager();
    }
    
    public function test_get_templates_by_category() {
        $business_templates = $this->template_manager->get_templates_by_category('business');
        $this->assertIsArray($business_templates);
        
        foreach ($business_templates as $template) {
            $this->assertEquals('business', $template['category']);
        }
    }
    
    public function test_template_file_exists() {
        $templates = $this->template_manager->get_available_templates();
        
        foreach ($templates as $template) {
            $template_file = RIFE_PG_PLUGIN_DIR . 'templates/' . $template['id'] . '/template.php';
            $this->assertFileExists($template_file);
        }
    }
    
    public function test_template_sections() {
        $template = $this->template_manager->get_template('business-basic');
        $this->assertArrayHasKey('sections', $template);
        $this->assertIsArray($template['sections']);
        $this->assertContains('hero', $template['sections']);
    }
}

/**
 * Test Style Customizer specifically
 */
class Test_Style_Customizer extends WP_UnitTestCase {
    
    private $style_customizer;
    
    public function setUp() {
        parent::setUp();
        $this->style_customizer = new Rife_PG_Style_Customizer();
    }
    
    public function test_css_generation() {
        $style_data = array(
            'primary_color' => '#ff0000',
            'secondary_color' => '#00ff00',
            'heading_font' => 'Arial',
            'body_font' => 'Helvetica'
        );
        
        $css = $this->style_customizer->generate_css($style_data);
        
        $this->assertStringContains('--primary-color: #ff0000', $css);
        $this->assertStringContains('--secondary-color: #00ff00', $css);
        $this->assertStringContains('font-family: "Arial"', $css);
    }
    
    public function test_color_validation() {
        $valid_colors = array('#ffffff', '#000000', '#ff0000');
        $invalid_colors = array('red', '#gggggg', 'invalid');
        
        foreach ($valid_colors as $color) {
            $this->assertTrue($this->style_customizer->is_valid_color($color));
        }
        
        foreach ($invalid_colors as $color) {
            $this->assertFalse($this->style_customizer->is_valid_color($color));
        }
    }
}
