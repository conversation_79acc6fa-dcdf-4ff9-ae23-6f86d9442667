<?php
/**
 * Test page generation directly
 * This file tests the page generation process step by step
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo '<h1>Test Page Generation</h1>';

// Test data
$test_template_id = 'business-basic';
$test_content_data = array(
    'hero' => array(
        'title' => 'Test Landing Page',
        'subtitle' => 'This is a test page to verify the plugin works correctly',
        'cta_text' => 'Get Started',
        'cta_link' => '#'
    ),
    'benefits' => array(
        'items' => array(
            array(
                'title' => 'Fast Performance',
                'description' => 'Lightning fast loading times',
                'icon' => 'fas fa-bolt'
            ),
            array(
                'title' => 'Secure',
                'description' => 'Enterprise-grade security',
                'icon' => 'fas fa-shield-alt'
            ),
            array(
                'title' => 'Easy to Use',
                'description' => 'Intuitive user interface',
                'icon' => 'fas fa-heart'
            )
        )
    )
);

$test_style_data = array(
    'primary_color' => '#007cba',
    'secondary_color' => '#50575e',
    'accent_color' => '#00a32a',
    'heading_font' => 'Inter',
    'body_font' => 'Inter'
);

echo '<h2>Step 1: Check Plugin Status</h2>';
if (function_exists('rife_pg')) {
    echo '<p style="color: green;">✓ Plugin loaded</p>';
    $plugin = rife_pg();
} else {
    echo '<p style="color: red;">✗ Plugin not loaded</p>';
    return;
}

echo '<h2>Step 2: Test Template Manager</h2>';
if (isset($plugin->template_manager)) {
    echo '<p style="color: green;">✓ Template Manager available</p>';
    
    $template = $plugin->template_manager->get_template($test_template_id);
    if ($template) {
        echo '<p style="color: green;">✓ Template "' . $test_template_id . '" found</p>';
        echo '<p>Template name: ' . $template['name'] . '</p>';
    } else {
        echo '<p style="color: red;">✗ Template "' . $test_template_id . '" not found</p>';
    }
} else {
    echo '<p style="color: red;">✗ Template Manager not available</p>';
    return;
}

echo '<h2>Step 3: Test Template Validation</h2>';
$validation_result = $plugin->template_manager->validate_template_data($test_template_id, $test_content_data);

if ($validation_result === true) {
    echo '<p style="color: green;">✓ Template validation passed</p>';
} else {
    echo '<p style="color: red;">✗ Template validation failed:</p>';
    if (is_array($validation_result)) {
        foreach ($validation_result as $error) {
            echo '<p style="color: red;">- ' . $error . '</p>';
        }
    }
    echo '<p><strong>Test content data:</strong></p>';
    echo '<pre>' . print_r($test_content_data, true) . '</pre>';
}

echo '<h2>Step 4: Test Page Generation</h2>';
if (isset($plugin->page_generator)) {
    echo '<p style="color: green;">✓ Page Generator available</p>';
    
    try {
        $result = $plugin->page_generator->generate_page($test_template_id, $test_content_data, $test_style_data);
        
        if ($result['success']) {
            echo '<p style="color: green;">✓ Page generated successfully!</p>';
            echo '<p><strong>Page ID:</strong> ' . $result['page_id'] . '</p>';
            echo '<p><strong>Page URL:</strong> <a href="' . $result['page_url'] . '" target="_blank">' . $result['page_url'] . '</a></p>';
            echo '<p><strong>Edit URL:</strong> <a href="' . $result['edit_url'] . '" target="_blank">' . $result['edit_url'] . '</a></p>';
        } else {
            echo '<p style="color: red;">✗ Page generation failed: ' . $result['message'] . '</p>';
        }
    } catch (Exception $e) {
        echo '<p style="color: red;">✗ Exception during page generation: ' . $e->getMessage() . '</p>';
        echo '<p>Stack trace:</p>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    }
} else {
    echo '<p style="color: red;">✗ Page Generator not available</p>';
}

echo '<h2>Step 5: Test AJAX Simulation</h2>';
echo '<p>Simulating AJAX request...</p>';

// Simulate AJAX request
$_POST['template_id'] = $test_template_id;
$_POST['content_data'] = $test_content_data;
$_POST['style_data'] = $test_style_data;
$_POST['auto_publish'] = 'false';
$_POST['nonce'] = wp_create_nonce('rife_pg_nonce');

if (isset($plugin->ajax_handler)) {
    echo '<p style="color: green;">✓ AJAX Handler available</p>';
    
    // Test sanitization
    $reflection = new ReflectionClass($plugin->ajax_handler);
    $sanitize_method = $reflection->getMethod('sanitize_content_data');
    $sanitize_method->setAccessible(true);
    
    $sanitized_data = $sanitize_method->invoke($plugin->ajax_handler, $test_content_data);
    
    echo '<p><strong>Original data:</strong></p>';
    echo '<pre>' . print_r($test_content_data, true) . '</pre>';
    
    echo '<p><strong>Sanitized data:</strong></p>';
    echo '<pre>' . print_r($sanitized_data, true) . '</pre>';
    
    if (isset($sanitized_data['hero']['title']) && !empty($sanitized_data['hero']['title'])) {
        echo '<p style="color: green;">✓ Hero title preserved after sanitization</p>';
    } else {
        echo '<p style="color: red;">✗ Hero title lost during sanitization</p>';
    }
} else {
    echo '<p style="color: red;">✗ AJAX Handler not available</p>';
}

echo '<h2>Step 6: Manual Form Test</h2>';
echo '<p>Test the form data collection manually:</p>';

echo '<form id="manual-test-form" style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">';
echo '<h3>Hero Section</h3>';
echo '<p><label>Title: <input type="text" name="content[hero][title]" value="Manual Test Title" style="width: 300px;" /></label></p>';
echo '<p><label>Subtitle: <textarea name="content[hero][subtitle]" style="width: 300px; height: 60px;">Manual test subtitle</textarea></label></p>';
echo '<p><label>CTA Text: <input type="text" name="content[hero][cta_text]" value="Click Here" style="width: 200px;" /></label></p>';
echo '<p><label>CTA Link: <input type="text" name="content[hero][cta_link]" value="#" style="width: 200px;" /></label></p>';

echo '<h3>Template Selection</h3>';
echo '<p><label>Template: <select id="template-selector">';
echo '<option value="business-basic">Business Basic</option>';
echo '<option value="tech-startup">Tech Startup</option>';
echo '<option value="ecommerce-product">E-commerce Product</option>';
echo '</select></label></p>';

echo '<p><button type="button" onclick="testManualGeneration()">Test Manual Generation</button></p>';
echo '</form>';

echo '<div id="manual-test-results"></div>';

echo '<script>
function testManualGeneration() {
    var formData = {
        template_id: document.getElementById("template-selector").value,
        content_data: {},
        style_data: {
            primary_color: "#007cba",
            secondary_color: "#50575e"
        },
        auto_publish: false
    };
    
    // Collect form data
    var form = document.getElementById("manual-test-form");
    var inputs = form.querySelectorAll("input, textarea, select");
    
    inputs.forEach(function(input) {
        var name = input.name;
        var value = input.value;
        
        if (name && name.includes("[hero][")) {
            if (!formData.content_data.hero) formData.content_data.hero = {};
            
            if (name.includes("[title]")) formData.content_data.hero.title = value;
            else if (name.includes("[subtitle]")) formData.content_data.hero.subtitle = value;
            else if (name.includes("[cta_text]")) formData.content_data.hero.cta_text = value;
            else if (name.includes("[cta_link]")) formData.content_data.hero.cta_link = value;
        }
    });
    
    console.log("Manual form data:", formData);
    
    // Show collected data
    document.getElementById("manual-test-results").innerHTML = 
        "<h3>Collected Form Data:</h3>" +
        "<pre>" + JSON.stringify(formData, null, 2) + "</pre>" +
        "<p><strong>Validation:</strong></p>" +
        "<p>Template ID: " + (formData.template_id ? "✓ Present" : "✗ Missing") + "</p>" +
        "<p>Hero Title: " + (formData.content_data.hero && formData.content_data.hero.title ? "✓ Present (" + formData.content_data.hero.title + ")" : "✗ Missing") + "</p>";
    
    // Test AJAX call
    if (typeof jQuery !== "undefined") {
        jQuery.ajax({
            url: "' . admin_url('admin-ajax.php') . '",
            type: "POST",
            data: {
                action: "rife_pg_generate_page",
                template_id: formData.template_id,
                content_data: formData.content_data,
                style_data: formData.style_data,
                auto_publish: formData.auto_publish,
                nonce: "' . wp_create_nonce('rife_pg_nonce') . '"
            },
            success: function(response) {
                console.log("AJAX Success:", response);
                document.getElementById("manual-test-results").innerHTML += 
                    "<h3>AJAX Result:</h3>" +
                    "<p style=\"color: green;\">✓ Success: " + JSON.stringify(response) + "</p>";
            },
            error: function(xhr, status, error) {
                console.log("AJAX Error:", xhr.responseText);
                document.getElementById("manual-test-results").innerHTML += 
                    "<h3>AJAX Result:</h3>" +
                    "<p style=\"color: red;\">✗ Error: " + xhr.responseText + "</p>";
            }
        });
    } else {
        document.getElementById("manual-test-results").innerHTML += 
            "<p style=\"color: orange;\">jQuery not available for AJAX test</p>";
    }
}
</script>';

echo '<h2>Troubleshooting Tips</h2>';
echo '<ul>';
echo '<li>Make sure the Hero Title field is not empty</li>';
echo '<li>Check browser console for JavaScript errors</li>';
echo '<li>Verify WordPress debug.log for PHP errors</li>';
echo '<li>Ensure proper file permissions</li>';
echo '<li>Try deactivating and reactivating the plugin</li>';
echo '</ul>';
?>
