# Template Development Guide

This comprehensive guide explains how to create new templates for the Rife PageGenerator plugin following the established patterns and conventions.

## Table of Contents

1. [Overview](#overview)
2. [File Structure](#file-structure)
3. [Required Files](#required-files)
4. [Placeholder System](#placeholder-system)
5. [Design Settings Integration](#design-settings-integration)
6. [Creating Template Files](#creating-template-files)
7. [Adding to Template Gallery](#adding-to-template-gallery)
8. [Best Practices](#best-practices)
9. [Common Pitfalls](#common-pitfalls)
10. [Testing Your Template](#testing-your-template)

## Overview

Templates in Rife PageGenerator follow a specific pattern that ensures compatibility with the plugin's features:

- **Placeholder-based content system** using `{{PLACEHOLDER_NAME}}` format
- **Design settings integration** using CSS custom properties
- **Dummy content system** for easy testing and demonstration
- **Modular structure** with consistent file organization

## File Structure

Each template must follow this exact directory structure:

```
templates/
└── your-template-name/
    ├── config.php          # Template configuration and field definitions
    ├── template.php        # Main template HTML/CSS/JS
    ├── defaults.php        # Dummy content and default values
    ├── preview.html        # Preview page for template gallery
    └── thumbnail.svg       # Template thumbnail (400x300px)
```

### Naming Conventions

- **Template ID**: Use lowercase with hyphens (e.g., `modern-business`, `tech-startup`)
- **Directory name**: Must match the template ID exactly
- **File names**: Use the exact names shown above (case-sensitive)

## Required Files

### 1. config.php

Defines template metadata and form field structure:

```php
<?php
return array(
    'id' => 'your-template-name',
    'name' => 'Your Template Name',
    'description' => 'Brief description of your template',
    'sections' => array(
        'hero' => array(
            'name' => 'Hero Section',
            'fields' => array(
                'title' => ['type' => 'text', 'label' => 'Main Title (H1)', 'required' => true],
                'subtitle' => ['type' => 'textarea', 'label' => 'Subtitle'],
                'cta_text' => ['type' => 'text', 'label' => 'CTA Button Text'],
                'cta_link' => ['type' => 'url', 'label' => 'CTA Button Link'],
            )
        ),
        // Add more sections as needed
    ),
    'style_options' => array(
        'typography' => array(
            'heading_font' => array(
                'label' => 'Heading Font',
                'type' => 'select',
                'default' => 'Inter',
                'options' => [
                    'Inter' => 'Inter',
                    'Roboto' => 'Roboto',
                    'Open Sans' => 'Open Sans',
                    // Add more font options
                ]
            )
        )
    )
);
```

### 2. template.php

Main template file with HTML, CSS, and JavaScript:

```php
<!-- Your Template Name -->
<!-- Template description -->

<!-- External CSS/JS includes -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

<style>
    :root {
        --primary-color: {{PRIMARY_COLOR}};
        --secondary-color: {{SECONDARY_COLOR}};
        --accent-color: {{ACCENT_COLOR}};
        --text-color: {{TEXT_COLOR}};
        --background-color: {{BACKGROUND_COLOR}};
        --section-padding: {{SECTION_PADDING}}px;
        --border-radius: {{BORDER_RADIUS}}px;
    }

    /* WordPress theme spacing reset - ALWAYS INCLUDE */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: var(--background-color);
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Comprehensive WordPress theme spacing fixes */
    .wp-site-blocks, .entry-content, .wp-block-post-content,
    header, .site-header, #masthead,
    footer, .site-footer, #colophon,
    .site-main, #main, .main-content,
    .site-content, #content,
    .site, #page, .hfeed,
    article, .post, .page,
    nav, .navigation, .nav-menu,
    .wp-block-group, .wp-block-columns, .wp-block-column {
        margin: 0 !important;
        padding: 0 !important;
    }

    html {
        margin-top: 0 !important;
    }

    /* Your template styles here */
</style>

<!-- Template HTML with placeholders -->
<section class="hero-section">
    <div class="container">
        <h1>{{HERO_TITLE}}</h1>
        <p>{{HERO_SUBTITLE}}</p>
        <a href="{{HERO_CTA_LINK}}" class="btn btn-primary">{{HERO_CTA_TEXT}}</a>
    </div>
</section>

<!-- JavaScript if needed -->
<script>
    // Your JavaScript here
</script>
```

### 3. defaults.php

Dummy content and default values:

```php
<?php
return array(
    // Hero Section
    'HERO_TITLE' => 'Your Default Hero Title',
    'HERO_SUBTITLE' => 'Your default subtitle text that demonstrates the template.',
    'HERO_CTA_TEXT' => 'Get Started',
    'HERO_CTA_LINK' => 'https://example.com/get-started',
    
    // Add all placeholders used in your template
    // Use descriptive, professional content
);
```

### 4. preview.html

Standalone preview page for the template gallery:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Template Preview</title>
    <!-- Include styles for preview -->
</head>
<body>
    <!-- Preview content showing template features -->
    <div class="preview-container">
        <h1>Your Template Name</h1>
        <p>Template description and features</p>
        <!-- Show key template elements -->
    </div>
</body>
</html>
```

### 5. thumbnail.svg

Template thumbnail (400x300px) showing template layout:

```svg
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
    <!-- SVG elements representing your template layout -->
    <rect width="400" height="300" fill="#ffffff"/>
    <!-- Add visual elements that represent your template -->
</svg>
```

## Placeholder System

### Naming Convention

- Use **UPPERCASE** with **UNDERSCORES**: `{{HERO_TITLE}}`, `{{CONTACT_EMAIL}}`
- Be **descriptive**: `{{PRICING_1_FEATURE_1}}` instead of `{{P1F1}}`
- Use **consistent prefixes** for related content: `{{BENEFIT_1_TITLE}}`, `{{BENEFIT_1_DESCRIPTION}}`

### Common Placeholder Patterns

```php
// Section titles and content
'{{SECTION_TITLE}}' => 'Section Title',
'{{SECTION_SUBTITLE}}' => 'Section subtitle text',

// Numbered items (benefits, features, etc.)
'{{BENEFIT_1_TITLE}}' => 'First Benefit Title',
'{{BENEFIT_1_DESCRIPTION}}' => 'First benefit description',
'{{BENEFIT_2_TITLE}}' => 'Second Benefit Title',

// Contact information
'{{CONTACT_EMAIL}}' => '<EMAIL>',
'{{CONTACT_PHONE}}' => '+****************',
'{{CONTACT_ADDRESS}}' => '123 Main St, City, State 12345',

// Call-to-action elements
'{{CTA_TEXT}}' => 'Get Started',
'{{CTA_LINK}}' => 'https://example.com/contact',

// Icons (FontAwesome classes)
'{{BENEFIT_1_ICON}}' => 'fas fa-rocket',
'{{CONTACT_ICON}}' => 'fas fa-envelope',
```

## Design Settings Integration

### Required CSS Custom Properties

Always include these CSS custom properties in your template:

```css
:root {
    --primary-color: {{PRIMARY_COLOR}};
    --secondary-color: {{SECONDARY_COLOR}};
    --accent-color: {{ACCENT_COLOR}};
    --text-color: {{TEXT_COLOR}};
    --background-color: {{BACKGROUND_COLOR}};
    --section-padding: {{SECTION_PADDING}}px;
    --border-radius: {{BORDER_RADIUS}}px;
}
```

### Using Design Settings

```css
/* Use CSS custom properties throughout your template */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--section-padding) 0;
}

.btn-primary {
    background-color: var(--primary-color);
    border-radius: var(--border-radius);
}

.card {
    border-radius: var(--border-radius);
    border-top: 3px solid var(--accent-color);
}
```

### Typography Integration

```css
/* Typography settings */
body {
    font-family: '{{BODY_FONT}}', sans-serif;
    font-size: {{BASE_FONT_SIZE}}px;
    color: var(--text-color);
}

h1, h2, h3, h4, h5, h6 {
    font-family: '{{HEADING_FONT}}', sans-serif;
}
```

## Creating Template Files

### Step 1: Plan Your Template

1. **Define sections**: Hero, Benefits, Pricing, Testimonials, etc.
2. **List required content**: What information will users need to provide?
3. **Design layout**: Sketch the visual structure
4. **Choose design elements**: Colors, fonts, spacing, components

### Step 2: Create Directory Structure

```bash
mkdir templates/your-template-name
cd templates/your-template-name
touch config.php template.php defaults.php preview.html thumbnail.svg
```

### Step 3: Build config.php

Start with the configuration file to define your template structure:

```php
<?php
return array(
    'id' => 'your-template-name',
    'name' => 'Your Template Display Name',
    'description' => 'Brief, compelling description of your template',
    'sections' => array(
        // Define each section with its fields
    ),
    'style_options' => array(
        // Define style customization options
    )
);
```

### Step 4: Create template.php

1. **Start with the CSS reset** (always include WordPress spacing fixes)
2. **Define CSS custom properties** for design settings
3. **Build HTML structure** with placeholders
4. **Add responsive design** with media queries
5. **Include JavaScript** if needed

### Step 5: Create defaults.php

Provide realistic, professional dummy content for all placeholders:

```php
<?php
return array(
    // Provide content for EVERY placeholder in your template
    'HERO_TITLE' => 'Professional, compelling title',
    'HERO_SUBTITLE' => 'Descriptive subtitle that explains the value proposition',
    // ... continue for all placeholders
);
```

### Step 6: Build preview.html

Create a standalone preview that showcases your template's key features and design.

### Step 7: Design thumbnail.svg

Create a visual representation of your template layout in SVG format (400x300px).

## Adding to Template Gallery

### Register Your Template

Add your template to the template manager in `includes/class-template-manager.php`:

```php
private function init_templates() {
    $this->templates = array(
        // Existing templates...
        
        'your-template-name' => array(
            'id' => 'your-template-name',
            'name' => __('Your Template Name', 'rife-pagegenerator'),
            'description' => __('Your template description', 'rife-pagegenerator'),
            'category' => 'business', // or appropriate category
            'thumbnail' => RIFE_PG_PLUGIN_URL . 'templates/your-template-name/thumbnail.svg',
            'preview_url' => RIFE_PG_PLUGIN_URL . 'templates/your-template-name/preview.html',
            'template_file' => 'your-template-name/template.php',
            'sections' => array('hero', 'benefits', 'pricing'), // List your sections
            'features' => array(
                'Modern Design',
                'Responsive Layout',
                'SEO Optimized',
                // List key features
            ),
            'word_count' => '1000+',
            'seo_score' => 'A',
            'recommended' => false // Set to true for featured templates
        )
    );
}
```

## Best Practices

### Content Guidelines

1. **Professional tone**: Use business-appropriate language
2. **Realistic content**: Avoid Lorem ipsum, use actual business content
3. **Consistent voice**: Maintain the same tone throughout
4. **Actionable CTAs**: Use compelling call-to-action text

### Design Guidelines

1. **Mobile-first**: Design for mobile devices first
2. **Accessibility**: Ensure good contrast ratios and semantic HTML
3. **Performance**: Optimize images and minimize CSS/JS
4. **Cross-browser**: Test in major browsers

### Code Guidelines

1. **Clean code**: Use proper indentation and comments
2. **Semantic HTML**: Use appropriate HTML5 elements
3. **CSS organization**: Group related styles together
4. **JavaScript**: Use modern ES6+ syntax when possible

### WordPress Integration

1. **Always include** the comprehensive CSS reset for WordPress themes
2. **Test with different themes** to ensure compatibility
3. **Use relative units** where possible for better theme integration
4. **Avoid conflicts** with common WordPress class names

## Common Pitfalls

### 1. Missing WordPress CSS Reset

**Problem**: Template appears broken when used with different WordPress themes.

**Solution**: Always include the comprehensive WordPress CSS reset:

```css
/* Comprehensive WordPress theme spacing fixes */
.wp-site-blocks, .entry-content, .wp-block-post-content,
header, .site-header, #masthead,
footer, .site-footer, #colophon,
.site-main, #main, .main-content,
.site-content, #content,
.site, #page, .hfeed,
article, .post, .page,
nav, .navigation, .nav-menu,
.wp-block-group, .wp-block-columns, .wp-block-column {
    margin: 0 !important;
    padding: 0 !important;
}
```

### 2. Inconsistent Placeholder Naming

**Problem**: Placeholders don't match between template.php and defaults.php.

**Solution**: 
- Use a consistent naming convention
- Double-check all placeholder names
- Test with dummy content filling

### 3. Missing Design Settings Integration

**Problem**: Template doesn't respond to color/font changes.

**Solution**: Always use CSS custom properties:

```css
:root {
    --primary-color: {{PRIMARY_COLOR}};
    /* ... other properties */
}

.element {
    color: var(--primary-color); /* Not: color: #007cba; */
}
```

### 4. Non-Responsive Design

**Problem**: Template doesn't work well on mobile devices.

**Solution**: Include responsive breakpoints:

```css
@media (max-width: 768px) {
    .section { padding: 60px 0; }
    .grid { grid-template-columns: 1fr; }
}
```

### 5. Missing Required Files

**Problem**: Template doesn't appear in gallery or causes errors.

**Solution**: Ensure all required files exist:
- config.php
- template.php  
- defaults.php
- preview.html
- thumbnail.svg

## Testing Your Template

### 1. Template Gallery Test

1. Navigate to **PageGenerator > Template Gallery**
2. Verify your template appears with correct thumbnail and description
3. Test the preview functionality

### 2. Page Generator Test

1. Navigate to **PageGenerator > Page Generator**
2. Select your template
3. Test "Fill Dummy Content" button
4. Verify all fields are populated correctly

### 3. Design Settings Test

1. Change colors in the Design Settings section
2. Modify typography settings
3. Adjust spacing options
4. Verify changes are reflected in the preview

### 4. Page Generation Test

1. Fill in content (or use dummy content)
2. Customize design settings
3. Generate a page
4. Verify the generated page displays correctly

### 5. Cross-Theme Test

1. Test with different WordPress themes
2. Verify no spacing issues or conflicts
3. Check mobile responsiveness
4. Test in different browsers

## Conclusion

Following this guide ensures your templates integrate seamlessly with the Rife PageGenerator plugin and provide a consistent, professional experience for users. Remember to test thoroughly and follow the established patterns for best results.

For questions or issues, refer to the existing templates (`seo-comprehensive` and `modern-business`) as reference implementations.
