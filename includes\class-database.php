<?php
/**
 * Database Management for Rife PageGenerator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rife_PG_Database {
    
    /**
     * Database version
     */
    const DB_VERSION = '1.0.0';
    
    /**
     * Table names
     */
    private $tables = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        
        $this->tables = array(
            'pages' => $wpdb->prefix . 'rife_pg_pages',
            'templates' => $wpdb->prefix . 'rife_pg_templates',
            'styles' => $wpdb->prefix . 'rife_pg_styles',
            'analytics' => $wpdb->prefix . 'rife_pg_analytics'
        );
        
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'check_database_version'));
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Pages table
        $pages_table = $this->tables['pages'];
        $pages_sql = "CREATE TABLE $pages_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            page_id bigint(20) unsigned NOT NULL,
            template_id varchar(100) NOT NULL,
            content_data longtext NOT NULL,
            style_data longtext NOT NULL,
            meta_data longtext,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY page_id (page_id),
            KEY template_id (template_id),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // Templates table
        $templates_table = $this->tables['templates'];
        $templates_sql = "CREATE TABLE $templates_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            template_id varchar(100) NOT NULL,
            name varchar(255) NOT NULL,
            description text,
            category varchar(100) NOT NULL,
            sections longtext NOT NULL,
            config longtext,
            thumbnail_url varchar(500),
            preview_url varchar(500),
            is_active tinyint(1) DEFAULT 1,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY template_id (template_id),
            KEY category (category),
            KEY is_active (is_active),
            KEY sort_order (sort_order)
        ) $charset_collate;";
        
        // Styles table
        $styles_table = $this->tables['styles'];
        $styles_sql = "CREATE TABLE $styles_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            style_data longtext NOT NULL,
            is_preset tinyint(1) DEFAULT 0,
            user_id bigint(20) unsigned DEFAULT NULL,
            usage_count int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY name (name),
            KEY is_preset (is_preset),
            KEY user_id (user_id),
            KEY usage_count (usage_count)
        ) $charset_collate;";
        
        // Analytics table
        $analytics_table = $this->tables['analytics'];
        $analytics_sql = "CREATE TABLE $analytics_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            page_id bigint(20) unsigned NOT NULL,
            template_id varchar(100) NOT NULL,
            event_type varchar(50) NOT NULL,
            event_data longtext,
            user_id bigint(20) unsigned DEFAULT NULL,
            ip_address varchar(45),
            user_agent text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY page_id (page_id),
            KEY template_id (template_id),
            KEY event_type (event_type),
            KEY user_id (user_id),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($pages_sql);
        dbDelta($templates_sql);
        dbDelta($styles_sql);
        dbDelta($analytics_sql);
        
        // Update database version
        update_option('rife_pg_db_version', self::DB_VERSION);
        
        // Insert default data
        $this->insert_default_data();
    }
    
    /**
     * Check database version and upgrade if needed
     */
    public function check_database_version() {
        $current_version = get_option('rife_pg_db_version', '0.0.0');
        
        if (version_compare($current_version, self::DB_VERSION, '<')) {
            $this->upgrade_database($current_version);
        }
    }
    
    /**
     * Upgrade database
     */
    private function upgrade_database($from_version) {
        // Perform version-specific upgrades
        if (version_compare($from_version, '1.0.0', '<')) {
            $this->create_tables();
        }
        
        // Add more version checks as needed
        // if (version_compare($from_version, '1.1.0', '<')) {
        //     $this->upgrade_to_1_1_0();
        // }
    }
    
    /**
     * Insert default data
     */
    private function insert_default_data() {
        $this->insert_default_templates();
        $this->insert_default_styles();
    }
    
    /**
     * Insert default templates
     */
    private function insert_default_templates() {
        global $wpdb;
        
        $templates_table = $this->tables['templates'];
        
        // Check if templates already exist
        $existing = $wpdb->get_var("SELECT COUNT(*) FROM $templates_table");
        if ($existing > 0) {
            return;
        }
        
        $default_templates = array(
            array(
                'template_id' => 'business-basic',
                'name' => 'Business Basic',
                'description' => 'A clean and professional business landing page template',
                'category' => 'business',
                'sections' => json_encode(array('hero', 'benefits', 'pricing', 'testimonials', 'contact')),
                'config' => json_encode(array(
                    'required_sections' => array('hero'),
                    'optional_sections' => array('benefits', 'pricing', 'testimonials', 'contact')
                )),
                'thumbnail_url' => RIFE_PG_PLUGIN_URL . 'templates/business-basic/thumbnail.svg',
                'preview_url' => RIFE_PG_PLUGIN_URL . 'templates/business-basic/preview.html',
                'sort_order' => 1
            ),
            array(
                'template_id' => 'tech-startup',
                'name' => 'Tech Startup',
                'description' => 'Modern tech startup landing page with gradient design',
                'category' => 'technology',
                'sections' => json_encode(array('hero', 'features', 'process', 'pricing', 'faq')),
                'config' => json_encode(array(
                    'required_sections' => array('hero'),
                    'optional_sections' => array('features', 'process', 'pricing', 'faq')
                )),
                'thumbnail_url' => RIFE_PG_PLUGIN_URL . 'templates/tech-startup/thumbnail.svg',
                'preview_url' => RIFE_PG_PLUGIN_URL . 'templates/tech-startup/preview.html',
                'sort_order' => 2
            ),
            array(
                'template_id' => 'ecommerce-product',
                'name' => 'E-commerce Product',
                'description' => 'Product showcase landing page for e-commerce',
                'category' => 'ecommerce',
                'sections' => json_encode(array('hero', 'benefits', 'social-proof', 'pricing', 'guarantee')),
                'config' => json_encode(array(
                    'required_sections' => array('hero'),
                    'optional_sections' => array('benefits', 'social-proof', 'pricing', 'guarantee')
                )),
                'thumbnail_url' => RIFE_PG_PLUGIN_URL . 'templates/ecommerce-product/thumbnail.svg',
                'preview_url' => RIFE_PG_PLUGIN_URL . 'templates/ecommerce-product/preview.html',
                'sort_order' => 3
            )
        );
        
        foreach ($default_templates as $template) {
            $wpdb->insert($templates_table, $template);
        }
    }
    
    /**
     * Insert default styles
     */
    private function insert_default_styles() {
        global $wpdb;
        
        $styles_table = $this->tables['styles'];
        
        // Check if styles already exist
        $existing = $wpdb->get_var("SELECT COUNT(*) FROM $styles_table WHERE is_preset = 1");
        if ($existing > 0) {
            return;
        }
        
        $default_styles = array(
            array(
                'name' => 'Modern Blue',
                'description' => 'Modern design with blue color scheme',
                'style_data' => json_encode(array(
                    'primary_color' => '#6366f1',
                    'secondary_color' => '#8b5cf6',
                    'accent_color' => '#06b6d4',
                    'heading_font' => 'Poppins',
                    'body_font' => 'Inter',
                    'border_radius' => '12',
                    'button_style' => 'rounded',
                    'shadow_style' => 'medium'
                )),
                'is_preset' => 1
            ),
            array(
                'name' => 'Classic Professional',
                'description' => 'Classic and professional design',
                'style_data' => json_encode(array(
                    'primary_color' => '#2563eb',
                    'secondary_color' => '#1e40af',
                    'accent_color' => '#059669',
                    'heading_font' => 'Georgia',
                    'body_font' => 'Times New Roman',
                    'border_radius' => '4',
                    'button_style' => 'square',
                    'shadow_style' => 'subtle'
                )),
                'is_preset' => 1
            ),
            array(
                'name' => 'Vibrant Creative',
                'description' => 'Vibrant and creative design',
                'style_data' => json_encode(array(
                    'primary_color' => '#ec4899',
                    'secondary_color' => '#f59e0b',
                    'accent_color' => '#10b981',
                    'heading_font' => 'Montserrat',
                    'body_font' => 'Open Sans',
                    'border_radius' => '20',
                    'button_style' => 'pill',
                    'shadow_style' => 'strong'
                )),
                'is_preset' => 1
            )
        );
        
        foreach ($default_styles as $style) {
            $wpdb->insert($styles_table, $style);
        }
    }
    
    /**
     * Get table name
     */
    public function get_table($table_key) {
        return isset($this->tables[$table_key]) ? $this->tables[$table_key] : null;
    }
    
    /**
     * Drop all tables
     */
    public function drop_tables() {
        global $wpdb;
        
        foreach ($this->tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
        
        delete_option('rife_pg_db_version');
    }
    
    /**
     * Get database statistics
     */
    public function get_statistics() {
        global $wpdb;
        
        $stats = array();
        
        foreach ($this->tables as $key => $table) {
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
            $stats[$key] = intval($count);
        }
        
        return $stats;
    }
    
    /**
     * Clean up old data
     */
    public function cleanup_old_data($days = 30) {
        global $wpdb;
        
        $date_threshold = date('Y-m-d H:i:s', strtotime("-$days days"));
        
        // Clean up old analytics data
        $analytics_table = $this->tables['analytics'];
        $wpdb->query($wpdb->prepare(
            "DELETE FROM $analytics_table WHERE created_at < %s",
            $date_threshold
        ));
        
        // Clean up orphaned page records
        $pages_table = $this->tables['pages'];
        $wpdb->query("
            DELETE p FROM $pages_table p 
            LEFT JOIN {$wpdb->posts} posts ON p.page_id = posts.ID 
            WHERE posts.ID IS NULL
        ");
    }
    
    /**
     * Optimize database tables
     */
    public function optimize_tables() {
        global $wpdb;
        
        foreach ($this->tables as $table) {
            $wpdb->query("OPTIMIZE TABLE $table");
        }
    }
    
    /**
     * Backup database tables
     */
    public function backup_tables() {
        global $wpdb;
        
        $backup_data = array();
        
        foreach ($this->tables as $key => $table) {
            $results = $wpdb->get_results("SELECT * FROM $table", ARRAY_A);
            $backup_data[$key] = $results;
        }
        
        return $backup_data;
    }
    
    /**
     * Restore database tables
     */
    public function restore_tables($backup_data) {
        global $wpdb;
        
        foreach ($backup_data as $key => $data) {
            if (!isset($this->tables[$key])) {
                continue;
            }
            
            $table = $this->tables[$key];
            
            // Clear existing data
            $wpdb->query("TRUNCATE TABLE $table");
            
            // Insert backup data
            foreach ($data as $row) {
                $wpdb->insert($table, $row);
            }
        }
    }
}
