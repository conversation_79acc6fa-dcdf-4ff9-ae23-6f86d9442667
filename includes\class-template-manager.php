<?php
/**
 * Template Manager for Rife PageGenerator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rife_PG_Template_Manager {
    
    /**
     * Available templates
     */
    private $templates = array();
    
    /**
     * Template categories
     */
    private $categories = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Initialize templates and categories with proper translation
     */
    public function init() {
        $this->init_templates();
        $this->init_categories();
    }
    
    /**
     * Initialize available templates
     */
    private function init_templates() {
        // Initialize templates with proper translation
        $this->templates = array(
            'seo-comprehensive' => array(
                'id' => 'seo-comprehensive',
                'name' => 'SEO Comprehensive',
                'description' => 'Template SEO-optimized dengan 13+ section untuk ranking maksimal (1500+ kata)',
                'category' => 'business',
                'thumbnail' => RIFE_PG_PLUGIN_URL . 'templates/seo-comprehensive/thumbnail.svg',
                'preview_url' => RIFE_PG_PLUGIN_URL . 'templates/seo-comprehensive/preview.html',
                'template_file' => 'seo-comprehensive/template.php',
                'sections' => array('hero', 'benefits', 'services', 'process', 'pricing', 'case_studies', 'guarantees', 'guide', 'faq', 'locations', 'about', 'testimonials', 'contact'),
                'features' => array(
                    'SEO-Optimized (1500+ words)',
                    '13+ Comprehensive Sections',
                    'Mobile-First Design',
                    'Core Web Vitals Ready',
                    'Schema Markup Support',
                    'FAQ for Long-tail Keywords',
                    'Local SEO Ready',
                    'E-E-A-T Compliant'
                ),
                'word_count' => '1500+',
                'seo_score' => 'A+',
                'recommended' => true
            )
        );

        // Allow themes and other plugins to add templates
        $this->templates = apply_filters('rife_pg_templates', $this->templates);
    }
    
    /**
     * Initialize available templates without translation (for early loading)
     */
    private function init_templates_direct() {
        $this->templates = array(
            'seo-comprehensive' => array(
                'id' => 'seo-comprehensive',
                'name' => 'SEO Comprehensive',
                'description' => 'Template SEO-optimized dengan 13+ section untuk ranking maksimal (1500+ kata)',
                'category' => 'business',
                'thumbnail' => RIFE_PG_PLUGIN_URL . 'templates/seo-comprehensive/thumbnail.svg',
                'preview_url' => RIFE_PG_PLUGIN_URL . 'templates/seo-comprehensive/preview.html',
                'template_file' => 'seo-comprehensive/template.php',
                'sections' => array('hero', 'benefits', 'services', 'process', 'pricing', 'case_studies', 'guarantees', 'guide', 'faq', 'locations', 'about', 'testimonials', 'contact'),
                'features' => array(
                    'SEO-Optimized (1500+ words)',
                    '13+ Comprehensive Sections',
                    'Mobile-First Design',
                    'Core Web Vitals Ready',
                    'Schema Markup Support',
                    'FAQ for Long-tail Keywords',
                    'Local SEO Ready',
                    'E-E-A-T Compliant'
                ),
                'word_count' => '1500+',
                'seo_score' => 'A+',
                'recommended' => true
            )
        );

        // Allow themes and other plugins to add templates
        $this->templates = apply_filters('rife_pg_templates', $this->templates);
    }
    
    /**
     * Initialize template categories without translation (for early loading)
     */
    private function init_categories_direct() {
        $this->categories = array(
            'business' => 'Business',
            'tech' => 'Technology',
            'ecommerce' => 'E-commerce',
            'agency' => 'Agency',
            'event' => 'Event',
            'education' => 'Education',
            'healthcare' => 'Healthcare',
            'nonprofit' => 'Non-profit'
        );
        
        // Allow themes and other plugins to add categories
        $this->categories = apply_filters('rife_pg_categories', $this->categories);
    }
    
    /**
     * Initialize template categories
     */
    private function init_categories() {
        $this->categories = array(
            'business' => __('Business', 'rife-pagegenerator'),
            'tech' => __('Technology', 'rife-pagegenerator'),
            'ecommerce' => __('E-commerce', 'rife-pagegenerator'),
            'agency' => __('Agency', 'rife-pagegenerator'),
            'event' => __('Event', 'rife-pagegenerator'),
            'education' => __('Education', 'rife-pagegenerator'),
            'healthcare' => __('Healthcare', 'rife-pagegenerator'),
            'nonprofit' => __('Non-profit', 'rife-pagegenerator')
        );
        
        // Allow themes and other plugins to add categories
        $this->categories = apply_filters('rife_pg_categories', $this->categories);
    }
    
    /**
     * Get all available templates
     */
    public function get_available_templates() {
        // If templates are not initialized yet, initialize them now
        if (empty($this->templates)) {
            $this->init_templates();
        }
        
        // Return templates
        return $this->templates;
    }
    
    /**
     * Get template by ID
     */
    public function get_template($template_id) {
        // If templates are not initialized yet, initialize them now
        if (empty($this->templates)) {
            $this->init_templates();
        }
        
        if (isset($this->templates[$template_id])) {
            // Return template
            return $this->templates[$template_id];
        }
        return null;
    }
    
    /**
     * Get templates by category
     */
    public function get_templates_by_category($category) {
        $filtered_templates = array();
        
        foreach ($this->templates as $template) {
            if ($template['category'] === $category) {
                $filtered_templates[] = $template;
            }
        }
        
        return $filtered_templates;
    }
    
    /**
     * Get template categories
     */
    public function get_template_categories() {
        // If categories are not initialized yet, initialize them now
        if (empty($this->categories)) {
            $this->init_categories();
        }
        
        // Return categories
        return $this->categories;
    }
    
    /**
     * Get template preview URL
     */
    public function get_template_preview_url($template_id) {
        $template = $this->get_template($template_id);
        return $template ? $template['preview_url'] : '';
    }
    
    /**
     * Get template content
     */
    public function get_template_content($template_id, $content_data = array(), $style_data = array()) {
        error_log('Rife PG: get_template_content called with template_id: ' . $template_id);

        $template = $this->get_template($template_id);

        if (!$template) {
            error_log('Rife PG: Template not found: ' . $template_id);
            return false;
        }

        error_log('Rife PG: Template found: ' . print_r($template, true));

        $template_file = RIFE_PG_PLUGIN_DIR . 'templates/' . $template['template_file'];
        error_log('Rife PG: Template file path: ' . $template_file);

        if (!file_exists($template_file)) {
            error_log('Rife PG: Template file does not exist: ' . $template_file);
            return false;
        }

        error_log('Rife PG: Template file exists, proceeding with content generation');
        
        // Start output buffering
        ob_start();

        // Make variables available to template
        $template_data = $template;
        $content = $content_data;
        $styles = $style_data;

        // Extract section data for easier access in templates
        $hero = isset($content_data['hero']) ? $content_data['hero'] : array();
        $benefits = isset($content_data['benefits']) ? $content_data['benefits'] : array();
        $pricing = isset($content_data['pricing']) ? $content_data['pricing'] : array();
        $testimonials = isset($content_data['testimonials']) ? $content_data['testimonials'] : array();
        $contact = isset($content_data['contact']) ? $content_data['contact'] : array();

        error_log('Rife PG: About to include template file with data: ' . print_r($content_data, true));

        // Include template file
        try {
            include $template_file;
            error_log('Rife PG: Template file included successfully');
        } catch (Exception $e) {
            error_log('Rife PG: Error including template file: ' . $e->getMessage());
            ob_end_clean();
            return false;
        }

        // Get content and clean buffer
        $html_content = ob_get_clean();
        error_log('Rife PG: Generated HTML content length: ' . strlen($html_content));
        
        // Apply style customizations
        $html_content = $this->apply_style_customizations($html_content, $style_data);
        
        return $html_content;
    }
    
    /**
     * Apply style customizations to template
     */
    private function apply_style_customizations($html_content, $style_data) {
        if (empty($style_data)) {
            return $html_content;
        }
        
        // Generate custom CSS
        $custom_css = $this->generate_custom_css($style_data);
        
        // Inject custom CSS into HTML
        if (!empty($custom_css)) {
            $css_tag = '<style type="text/css">' . $custom_css . '</style>';
            
            // Try to inject before closing head tag
            if (strpos($html_content, '</head>') !== false) {
                $html_content = str_replace('</head>', $css_tag . '</head>', $html_content);
            } else {
                // If no head tag, prepend to content
                $html_content = $css_tag . $html_content;
            }
        }
        
        return $html_content;
    }
    
    /**
     * Generate custom CSS from style data
     */
    private function generate_custom_css($style_data) {
        $css = '';
        
        // Primary color
        if (!empty($style_data['primary_color'])) {
            $css .= ':root { --primary-color: ' . $style_data['primary_color'] . '; }';
        }
        
        // Secondary color
        if (!empty($style_data['secondary_color'])) {
            $css .= ':root { --secondary-color: ' . $style_data['secondary_color'] . '; }';
        }
        
        // Typography
        if (!empty($style_data['heading_font'])) {
            $css .= 'h1, h2, h3, h4, h5, h6 { font-family: "' . $style_data['heading_font'] . '", sans-serif; }';
        }
        
        if (!empty($style_data['body_font'])) {
            $css .= 'body, p, div { font-family: "' . $style_data['body_font'] . '", sans-serif; }';
        }
        
        // Spacing
        if (!empty($style_data['section_padding'])) {
            $css .= '.section { padding: ' . $style_data['section_padding'] . 'px 0; }';
        }
        
        // Border radius
        if (!empty($style_data['border_radius'])) {
            $css .= '.btn, .card, .form-control { border-radius: ' . $style_data['border_radius'] . 'px; }';
        }
        
        return $css;
    }
    
    /**
     * Get template sections
     */
    public function get_template_sections($template_id) {
        $template = $this->get_template($template_id);
        return $template ? $template['sections'] : array();
    }
    
    /**
     * Validate template data
     */
    public function validate_template_data($template_id, $content_data) {
        $template = $this->get_template($template_id);

        if (!$template) {
            return array('Template not found');
        }

        $errors = array();

        // Very basic validation - only check if hero title exists and is not empty
        if (!isset($content_data['hero']) ||
            !isset($content_data['hero']['title']) ||
            trim($content_data['hero']['title']) === '') {
            $errors[] = __('Hero title is required', 'rife-pagegenerator');
        }

        // Skip other validations for now to avoid blocking page generation

        return empty($errors) ? true : $errors;
    }
}
