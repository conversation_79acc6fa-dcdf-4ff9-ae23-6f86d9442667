<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== QUICK TEST ===\n";

echo "1. Loading Image Generator...\n";
require_once dirname(__FILE__) . '/../modules/class-image-generator.php';

if (class_exists('Rife_PG_Image_Generator')) {
    echo "✅ Class loaded successfully\n";
    
    echo "2. Creating instance...\n";
    $gen = new Rife_PG_Image_Generator();
    echo "✅ Instance created\n";
    
    echo "3. Testing image generation...\n";
    $result = $gen->generate_image('Test Image', 'test keyword');
    
    if ($result['success']) {
        echo "✅ Image generated successfully!\n";
        echo "   Filename: " . $result['filename'] . "\n";
        echo "   ALT Text: " . $result['alt_text'] . "\n";
        
        if (file_exists($result['path'])) {
            $size = filesize($result['path']);
            echo "   File Size: " . number_format($size) . " bytes\n";
            
            // Cleanup
            unlink($result['path']);
            echo "✅ Test file cleaned up\n";
        }
    } else {
        echo "❌ Image generation failed: " . $result['error'] . "\n";
    }
    
} else {
    echo "❌ Class not found\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
