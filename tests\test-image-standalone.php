<?php
/**
 * Standalone Image Generator Test
 * 
 * This test creates a simplified version of the image generator
 * that can work without WordPress functions for testing purposes.
 */

echo "=== Standalone Image Generator Test ===\n";

class Simple_Image_Generator {
    
    private $width = 1200;
    private $height = 630;
    private $font_size = 48;
    
    private $background_colors = [
        ['r' => 59, 'g' => 130, 'b' => 246],  // Blue
        ['r' => 16, 'g' => 185, 'b' => 129],  // Green
        ['r' => 139, 'g' => 92, 'b' => 246],  // Purple
        ['r' => 245, 'g' => 158, 'b' => 11],  // Orange
        ['r' => 236, 'g' => 72, 'b' => 153],  // Pink
    ];
    
    public function generate_image($text, $keyword = '') {
        try {
            echo "Starting image generation for: $text\n";
            
            // Validate input
            if (empty($text)) {
                throw new Exception('Text parameter is empty');
            }
            
            // Check if GD library is available
            if (!extension_loaded('gd')) {
                throw new Exception('GD library is not available');
            }
            
            // Create temp directory for testing
            $temp_dir = sys_get_temp_dir();
            if (!is_writable($temp_dir)) {
                throw new Exception('Temp directory is not writable: ' . $temp_dir);
            }
            
            // Create image resource
            $image = imagecreatetruecolor($this->width, $this->height);
            if (!$image) {
                throw new Exception('Failed to create image resource');
            }
            
            echo "✅ Image resource created\n";
            
            // Select random background color
            $bg_color = $this->background_colors[array_rand($this->background_colors)];
            $background = imagecolorallocate($image, $bg_color['r'], $bg_color['g'], $bg_color['b']);
            imagefill($image, 0, 0, $background);
            
            echo "✅ Background color applied\n";
            
            // Add gradient effect
            $this->add_gradient($image, $bg_color);
            echo "✅ Gradient effect added\n";
            
            // Add text
            $text_color = imagecolorallocate($image, 255, 255, 255);
            $this->add_text($image, $text, $text_color);
            echo "✅ Text added\n";
            
            // Add decorative elements
            $this->add_decorative_elements($image, $bg_color);
            echo "✅ Decorative elements added\n";
            
            // Generate filename
            $filename = 'test-image-' . time() . '.jpg';
            $image_path = $temp_dir . DIRECTORY_SEPARATOR . $filename;
            
            // Save image
            $saved = imagejpeg($image, $image_path, 90);
            if (!$saved) {
                throw new Exception('Failed to save image to: ' . $image_path);
            }
            
            // Clean up
            imagedestroy($image);
            
            echo "✅ Image saved successfully: $image_path\n";
            
            // Generate ALT text
            $alt_text = $this->generate_alt_text($text, $keyword);
            
            return [
                'success' => true,
                'path' => $image_path,
                'filename' => $filename,
                'alt_text' => $alt_text,
                'size' => filesize($image_path)
            ];
            
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    private function add_gradient($image, $base_color) {
        // Create darker version for gradient
        $darker_color = imagecolorallocate(
            $image,
            max(0, $base_color['r'] - 30),
            max(0, $base_color['g'] - 30),
            max(0, $base_color['b'] - 30)
        );
        
        // Add gradient from top to bottom
        for ($y = 0; $y < $this->height; $y++) {
            $alpha = 1 - ($y / $this->height);
            $color = imagecolorallocate(
                $image,
                max(0, min(255, (int)($base_color['r'] * $alpha + ($base_color['r'] - 30) * (1 - $alpha)))),
                max(0, min(255, (int)($base_color['g'] * $alpha + ($base_color['g'] - 30) * (1 - $alpha)))),
                max(0, min(255, (int)($base_color['b'] * $alpha + ($base_color['b'] - 30) * (1 - $alpha))))
            );
            
            imageline($image, 0, $y, $this->width, $y, $color);
        }
    }
    
    private function add_text($image, $text, $text_color) {
        // Clean and truncate text
        $text = substr($text, 0, 50); // Max 50 characters
        
        // Calculate text positioning (center)
        $font = 5; // Largest built-in font
        $text_width = strlen($text) * 10; // Approximate width
        $text_height = 16;
        
        $x = ($this->width - $text_width) / 2;
        $y = ($this->height - $text_height) / 2;
        
        // Add text shadow
        $shadow_color = imagecolorallocate($image, 0, 0, 0);
        imagestring($image, $font, $x + 2, $y + 2, $text, $shadow_color);
        imagestring($image, $font, $x, $y, $text, $text_color);
    }
    
    private function add_decorative_elements($image, $base_color) {
        // Add some geometric shapes
        $shape_color = imagecolorallocate($image, 255, 255, 255);
        
        // Add circles as decorative elements
        for ($i = 0; $i < 3; $i++) {
            $x = rand(50, $this->width - 50);
            $y = rand(50, $this->height - 50);
            $size = rand(20, 60);
            
            imagefilledellipse($image, $x, $y, $size, $size, $shape_color);
        }
    }
    
    private function generate_alt_text($text, $keyword) {
        if ($keyword) {
            return $keyword . ' services - ' . $text;
        }
        return 'Professional services - ' . $text;
    }
}

// Run the test
echo "\n1. Checking GD Library...\n";
if (extension_loaded('gd')) {
    echo "✅ GD Library is available\n";
    $gd_info = gd_info();
    echo "   - Version: " . $gd_info['GD Version'] . "\n";
    echo "   - JPEG Support: " . ($gd_info['JPEG Support'] ? 'YES' : 'NO') . "\n";
} else {
    echo "❌ GD Library not available\n";
    exit(1);
}

echo "\n2. Testing Image Generation...\n";

$generator = new Simple_Image_Generator();

// Test 1: Basic image generation
echo "\nTest 1: Basic Image Generation\n";
$result1 = $generator->generate_image('SEO Services Jakarta', 'seo jakarta');

if ($result1['success']) {
    echo "✅ Basic image generation: SUCCESS\n";
    echo "   - File: " . $result1['filename'] . "\n";
    echo "   - Size: " . number_format($result1['size']) . " bytes\n";
    echo "   - ALT Text: " . $result1['alt_text'] . "\n";
} else {
    echo "❌ Basic image generation: FAILED\n";
}

// Test 2: Different keyword
echo "\nTest 2: Different Keyword\n";
$result2 = $generator->generate_image('Digital Marketing Excellence', 'digital marketing');

if ($result2['success']) {
    echo "✅ Different keyword: SUCCESS\n";
    echo "   - ALT Text: " . $result2['alt_text'] . "\n";
} else {
    echo "❌ Different keyword: FAILED\n";
}

// Test 3: Empty keyword
echo "\nTest 3: Empty Keyword\n";
$result3 = $generator->generate_image('Web Design Services', '');

if ($result3['success']) {
    echo "✅ Empty keyword: SUCCESS\n";
    echo "   - ALT Text: " . $result3['alt_text'] . "\n";
} else {
    echo "❌ Empty keyword: FAILED\n";
}

// Test 4: Error handling
echo "\nTest 4: Error Handling\n";
$result4 = $generator->generate_image('', 'test');

if (!$result4['success']) {
    echo "✅ Error handling: SUCCESS (correctly failed with empty text)\n";
    echo "   - Error: " . $result4['error'] . "\n";
} else {
    echo "❌ Error handling: FAILED (should have failed with empty text)\n";
}

// Cleanup
echo "\n3. Cleanup...\n";
$cleaned = 0;
$results = [$result1, $result2, $result3];

foreach ($results as $result) {
    if ($result['success'] && file_exists($result['path'])) {
        if (unlink($result['path'])) {
            $cleaned++;
        }
    }
}

echo "✅ Cleaned up $cleaned test files\n";

echo "\n=== Image Generator Test Complete ===\n";
echo "✅ All image generation functionality is working correctly!\n";
?>
