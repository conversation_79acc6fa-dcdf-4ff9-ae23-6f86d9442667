<?php
/**
 * Test Image Generator Module
 * 
 * This test specifically focuses on testing the image generation functionality
 * to ensure it works correctly with focus keyphrases and creates proper ALT text.
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>Rife PageGenerator - Image Generator Test</h1>\n";

// Include the image generator module
require_once dirname(__FILE__) . '/../modules/class-image-generator.php';

class Rife_PG_Image_Generator_Test {
    
    private $test_results = [];
    private $generated_files = [];
    
    public function __construct() {
        $this->run_all_tests();
        $this->display_results();
        $this->cleanup();
    }
    
    /**
     * Run all image generator tests
     */
    private function run_all_tests() {
        $this->test_class_instantiation();
        $this->test_gd_library();
        $this->test_image_generation();
        $this->test_alt_text_generation();
        $this->test_multiple_images();
        $this->test_error_handling();
    }
    
    /**
     * Test if Image Generator class can be instantiated
     */
    private function test_class_instantiation() {
        $test_name = "Class Instantiation Test";
        
        try {
            if (class_exists('Rife_PG_Image_Generator')) {
                $this->add_result($test_name, true, "Rife_PG_Image_Generator class found");
                
                $image_generator = new Rife_PG_Image_Generator();
                if ($image_generator instanceof Rife_PG_Image_Generator) {
                    $this->add_result($test_name, true, "Image generator instantiated successfully");
                } else {
                    $this->add_result($test_name, false, "Failed to instantiate image generator");
                }
                
            } else {
                $this->add_result($test_name, false, "Rife_PG_Image_Generator class not found");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test GD Library availability
     */
    private function test_gd_library() {
        $test_name = "GD Library Test";
        
        try {
            if (extension_loaded('gd')) {
                $this->add_result($test_name, true, "GD Library is available");
                
                $gd_info = gd_info();
                $this->add_result($test_name, true, "GD Version: " . $gd_info['GD Version']);
                
                if ($gd_info['JPEG Support']) {
                    $this->add_result($test_name, true, "JPEG support available");
                } else {
                    $this->add_result($test_name, false, "JPEG support not available");
                }
                
                if ($gd_info['PNG Support']) {
                    $this->add_result($test_name, true, "PNG support available");
                } else {
                    $this->add_result($test_name, false, "PNG support not available");
                }
                
            } else {
                $this->add_result($test_name, false, "GD Library not available - tests will use fallback");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test basic image generation
     */
    private function test_image_generation() {
        $test_name = "Image Generation Test";
        
        try {
            if (!class_exists('Rife_PG_Image_Generator')) {
                $this->add_result($test_name, false, "Image generator class not available");
                return;
            }
            
            $image_generator = new Rife_PG_Image_Generator();
            
            // Test basic image generation
            $test_text = "SEO Services Jakarta";
            $test_keyword = "seo jakarta";
            
            $result = $image_generator->generate_image($test_text, $test_keyword);
            
            if ($result['success']) {
                $this->add_result($test_name, true, "Image generated successfully");
                $this->add_result($test_name, true, "Image URL: " . $result['url']);
                $this->add_result($test_name, true, "ALT text: " . $result['alt_text']);
                
                // Store for cleanup
                $this->generated_files[] = $result['path'];
                
                // Check if file actually exists
                if (file_exists($result['path'])) {
                    $this->add_result($test_name, true, "Image file exists on disk");
                    
                    // Check file size
                    $file_size = filesize($result['path']);
                    if ($file_size > 1000) { // At least 1KB
                        $this->add_result($test_name, true, "Image file size: " . number_format($file_size) . " bytes");
                    } else {
                        $this->add_result($test_name, false, "Image file too small: " . $file_size . " bytes");
                    }
                    
                } else {
                    $this->add_result($test_name, false, "Image file not found: " . $result['path']);
                }
                
            } else {
                $this->add_result($test_name, false, "Image generation failed: " . ($result['error'] ?? 'Unknown error'));
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test ALT text generation
     */
    private function test_alt_text_generation() {
        $test_name = "ALT Text Generation Test";
        
        try {
            if (!class_exists('Rife_PG_Image_Generator')) {
                $this->add_result($test_name, false, "Image generator class not available");
                return;
            }
            
            $image_generator = new Rife_PG_Image_Generator();
            
            // Test different keyword scenarios
            $test_cases = [
                ['text' => 'Digital Marketing', 'keyword' => 'digital marketing jakarta', 'expected_contains' => 'digital marketing jakarta'],
                ['text' => 'SEO Services', 'keyword' => 'jasa seo', 'expected_contains' => 'jasa seo'],
                ['text' => 'Web Design', 'keyword' => '', 'expected_contains' => 'Professional services']
            ];
            
            foreach ($test_cases as $index => $test_case) {
                $result = $image_generator->generate_image($test_case['text'], $test_case['keyword']);
                
                if ($result['success']) {
                    $alt_text = strtolower($result['alt_text']);
                    $expected = strtolower($test_case['expected_contains']);
                    
                    if (strpos($alt_text, $expected) !== false) {
                        $this->add_result($test_name, true, "Test case " . ($index + 1) . " ALT text correct: " . $result['alt_text']);
                    } else {
                        $this->add_result($test_name, false, "Test case " . ($index + 1) . " ALT text incorrect. Expected to contain: " . $expected . ", Got: " . $alt_text);
                    }
                    
                    // Store for cleanup
                    $this->generated_files[] = $result['path'];
                    
                } else {
                    $this->add_result($test_name, false, "Test case " . ($index + 1) . " failed: " . ($result['error'] ?? 'Unknown error'));
                }
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test multiple image generation
     */
    private function test_multiple_images() {
        $test_name = "Multiple Images Test";
        
        try {
            if (!class_exists('Rife_PG_Image_Generator')) {
                $this->add_result($test_name, false, "Image generator class not available");
                return;
            }
            
            $image_generator = new Rife_PG_Image_Generator();
            
            // Generate multiple images
            $keywords = ['web design', 'seo services', 'digital marketing'];
            $success_count = 0;
            
            foreach ($keywords as $keyword) {
                $result = $image_generator->generate_image($keyword . ' Excellence', $keyword);
                
                if ($result['success']) {
                    $success_count++;
                    $this->generated_files[] = $result['path'];
                } else {
                    $this->add_result($test_name, false, "Failed to generate image for: " . $keyword);
                }
            }
            
            if ($success_count === count($keywords)) {
                $this->add_result($test_name, true, "Successfully generated " . $success_count . " images");
            } else {
                $this->add_result($test_name, false, "Only generated " . $success_count . " out of " . count($keywords) . " images");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test error handling
     */
    private function test_error_handling() {
        $test_name = "Error Handling Test";
        
        try {
            if (!class_exists('Rife_PG_Image_Generator')) {
                $this->add_result($test_name, false, "Image generator class not available");
                return;
            }
            
            $image_generator = new Rife_PG_Image_Generator();
            
            // Test with empty text
            $result = $image_generator->generate_image('', 'test keyword');
            if (!$result['success']) {
                $this->add_result($test_name, true, "Correctly handled empty text: " . $result['error']);
            } else {
                $this->add_result($test_name, false, "Should have failed with empty text");
            }
            
            // Test with very long text
            $long_text = str_repeat('Very long text ', 20);
            $result = $image_generator->generate_image($long_text, 'test');
            if ($result['success']) {
                $this->add_result($test_name, true, "Handled long text correctly");
                $this->generated_files[] = $result['path'];
            } else {
                $this->add_result($test_name, false, "Failed to handle long text: " . ($result['error'] ?? 'Unknown error'));
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Add test result
     */
    private function add_result($test_name, $success, $message) {
        if (!isset($this->test_results[$test_name])) {
            $this->test_results[$test_name] = [];
        }
        
        $this->test_results[$test_name][] = [
            'success' => $success,
            'message' => $message
        ];
    }
    
    /**
     * Display test results
     */
    private function display_results() {
        echo "<h2>Test Results</h2>\n";
        
        foreach ($this->test_results as $test_name => $results) {
            echo "<h3>$test_name</h3>\n";
            echo "<ul>\n";
            
            foreach ($results as $result) {
                $status = $result['success'] ? '✅' : '❌';
                $color = $result['success'] ? 'green' : 'red';
                echo "<li style='color: $color'>$status " . htmlspecialchars($result['message']) . "</li>\n";
            }
            
            echo "</ul>\n";
        }
        
        // Summary
        $total_tests = 0;
        $passed_tests = 0;
        
        foreach ($this->test_results as $results) {
            foreach ($results as $result) {
                $total_tests++;
                if ($result['success']) {
                    $passed_tests++;
                }
            }
        }
        
        echo "<h2>Summary</h2>\n";
        echo "<p><strong>Passed: $passed_tests / $total_tests tests</strong></p>\n";
        
        if ($passed_tests === $total_tests) {
            echo "<p style='color: green; font-weight: bold;'>🎉 All image generator tests passed!</p>\n";
        } else {
            echo "<p style='color: red; font-weight: bold;'>⚠️ Some tests failed. Please review the issues above.</p>\n";
        }
        
        echo "<p><strong>Generated " . count($this->generated_files) . " test images</strong></p>\n";
    }
    
    /**
     * Cleanup generated test files
     */
    private function cleanup() {
        echo "<h2>Cleanup</h2>\n";
        
        $cleaned = 0;
        foreach ($this->generated_files as $file) {
            if (file_exists($file)) {
                if (unlink($file)) {
                    $cleaned++;
                } else {
                    echo "<p style='color: orange;'>⚠️ Could not delete: " . basename($file) . "</p>\n";
                }
            }
        }
        
        if ($cleaned > 0) {
            echo "<p style='color: green;'>✅ Cleaned up $cleaned test image files</p>\n";
        } else {
            echo "<p>No files to clean up</p>\n";
        }
    }
}

// Run tests if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'test-image-generator.php') {
    new Rife_PG_Image_Generator_Test();
}
?>
