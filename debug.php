<?php
/**
 * Debug file for Rife PageGenerator
 * This file helps debug issues with the plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo '<h1>Rife PageGenerator Debug Information</h1>';

// Check if plugin is loaded
echo '<h2>Plugin Status</h2>';
if (function_exists('rife_pg')) {
    echo '<p style="color: green;">✓ Plugin is loaded</p>';
    
    $plugin = rife_pg();
    echo '<p>Plugin instance: ' . get_class($plugin) . '</p>';
    
    // Check components
    if (isset($plugin->template_manager)) {
        echo '<p style="color: green;">✓ Template Manager loaded</p>';
    } else {
        echo '<p style="color: red;">✗ Template Manager not loaded</p>';
    }
    
    if (isset($plugin->page_generator)) {
        echo '<p style="color: green;">✓ Page Generator loaded</p>';
    } else {
        echo '<p style="color: red;">✗ Page Generator not loaded</p>';
    }
    
    if (isset($plugin->ajax_handler)) {
        echo '<p style="color: green;">✓ AJAX Handler loaded</p>';
    } else {
        echo '<p style="color: red;">✗ AJAX Handler not loaded</p>';
    }
    
    if (isset($plugin->database)) {
        echo '<p style="color: green;">✓ Database class loaded</p>';
    } else {
        echo '<p style="color: red;">✗ Database class not loaded</p>';
    }
    
} else {
    echo '<p style="color: red;">✗ Plugin is not loaded</p>';
}

// Check database tables
echo '<h2>Database Tables</h2>';
global $wpdb;

$tables = array(
    'rife_pg_pages' => $wpdb->prefix . 'rife_pg_pages',
    'rife_pg_templates' => $wpdb->prefix . 'rife_pg_templates',
    'rife_pg_styles' => $wpdb->prefix . 'rife_pg_styles',
    'rife_pg_analytics' => $wpdb->prefix . 'rife_pg_analytics'
);

foreach ($tables as $name => $table) {
    $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'");
    if ($exists) {
        $count = $wpdb->get_var("SELECT COUNT(*) FROM $table");
        echo '<p style="color: green;">✓ ' . $name . ' exists (' . $count . ' records)</p>';
    } else {
        echo '<p style="color: red;">✗ ' . $name . ' does not exist</p>';
    }
}

// Check templates
echo '<h2>Templates</h2>';
if (function_exists('rife_pg') && isset(rife_pg()->template_manager)) {
    $templates = rife_pg()->template_manager->get_available_templates();
    if (!empty($templates)) {
        echo '<p style="color: green;">✓ Found ' . count($templates) . ' templates</p>';
        foreach ($templates as $template) {
            echo '<p>- ' . $template['name'] . ' (' . $template['id'] . ')</p>';
        }
    } else {
        echo '<p style="color: red;">✗ No templates found</p>';
    }
} else {
    echo '<p style="color: red;">✗ Cannot check templates - Template Manager not available</p>';
}

// Check template files
echo '<h2>Template Files</h2>';
$template_dirs = array('business-basic', 'tech-startup', 'ecommerce-product');

foreach ($template_dirs as $dir) {
    $template_file = RIFE_PG_PLUGIN_DIR . 'templates/' . $dir . '/template.php';
    $preview_file = RIFE_PG_PLUGIN_DIR . 'templates/' . $dir . '/preview.html';
    $thumbnail_file = RIFE_PG_PLUGIN_DIR . 'templates/' . $dir . '/thumbnail.svg';
    
    echo '<h3>' . $dir . '</h3>';
    echo '<p>Template file: ' . (file_exists($template_file) ? '<span style="color: green;">✓ Exists</span>' : '<span style="color: red;">✗ Missing</span>') . '</p>';
    echo '<p>Preview file: ' . (file_exists($preview_file) ? '<span style="color: green;">✓ Exists</span>' : '<span style="color: red;">✗ Missing</span>') . '</p>';
    echo '<p>Thumbnail file: ' . (file_exists($thumbnail_file) ? '<span style="color: green;">✓ Exists</span>' : '<span style="color: red;">✗ Missing</span>') . '</p>';
}

// Check AJAX actions
echo '<h2>AJAX Actions</h2>';
$ajax_actions = array(
    'rife_pg_preview_template',
    'rife_pg_generate_page',
    'rife_pg_delete_page',
    'rife_pg_duplicate_page'
);

foreach ($ajax_actions as $action) {
    $has_action = has_action('wp_ajax_' . $action);
    echo '<p>' . $action . ': ' . ($has_action ? '<span style="color: green;">✓ Registered</span>' : '<span style="color: red;">✗ Not registered</span>') . '</p>';
}

// Check WordPress version and requirements
echo '<h2>System Requirements</h2>';
global $wp_version;
echo '<p>WordPress Version: ' . $wp_version . '</p>';
echo '<p>PHP Version: ' . PHP_VERSION . '</p>';
echo '<p>MySQL Version: ' . $wpdb->db_version() . '</p>';

// Check plugin constants
echo '<h2>Plugin Constants</h2>';
$constants = array(
    'RIFE_PG_PLUGIN_DIR',
    'RIFE_PG_PLUGIN_URL',
    'RIFE_PG_PLUGIN_BASENAME',
    'RIFE_PG_VERSION'
);

foreach ($constants as $constant) {
    if (defined($constant)) {
        echo '<p>' . $constant . ': <code>' . constant($constant) . '</code></p>';
    } else {
        echo '<p style="color: red;">' . $constant . ': Not defined</p>';
    }
}

// Test form data collection
echo '<h2>Test Form Data</h2>';
echo '<form id="test-form">';
echo '<p><label>Hero Title: <input type="text" name="content[hero][title]" value="Test Title" /></label></p>';
echo '<p><label>Hero Subtitle: <textarea name="content[hero][subtitle]">Test Subtitle</textarea></label></p>';
echo '<p><button type="button" onclick="testFormData()">Test Form Data Collection</button></p>';
echo '</form>';

echo '<div id="test-results"></div>';

echo '<script>
function testFormData() {
    var formData = {
        template_id: "business-basic",
        content_data: {},
        style_data: {},
        auto_publish: false
    };
    
    // Collect form data
    document.querySelectorAll("#test-form input, #test-form textarea").forEach(function(field) {
        var name = field.name;
        var value = field.value;
        
        if (name.includes("[hero][title]")) {
            if (!formData.content_data.hero) formData.content_data.hero = {};
            formData.content_data.hero.title = value;
        } else if (name.includes("[hero][subtitle]")) {
            if (!formData.content_data.hero) formData.content_data.hero = {};
            formData.content_data.hero.subtitle = value;
        }
    });
    
    document.getElementById("test-results").innerHTML = "<h3>Collected Data:</h3><pre>" + JSON.stringify(formData, null, 2) + "</pre>";
}
</script>';

// Check error logs
echo '<h2>Recent Error Logs</h2>';
$log_file = WP_CONTENT_DIR . '/debug.log';
if (file_exists($log_file)) {
    $logs = file_get_contents($log_file);
    $rife_logs = array();
    $lines = explode("\n", $logs);
    
    foreach ($lines as $line) {
        if (strpos($line, 'Rife PG') !== false) {
            $rife_logs[] = $line;
        }
    }
    
    if (!empty($rife_logs)) {
        echo '<div style="background: #f0f0f0; padding: 10px; max-height: 300px; overflow-y: scroll;">';
        foreach (array_slice($rife_logs, -10) as $log) {
            echo '<p>' . esc_html($log) . '</p>';
        }
        echo '</div>';
    } else {
        echo '<p>No Rife PG related errors found in logs</p>';
    }
} else {
    echo '<p>Debug log file not found</p>';
}
?>
