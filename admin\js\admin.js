/**
 * Admin JavaScript for Rife PageGenerator
 */

jQuery(document).ready(function($) {
    'use strict';

    // --- Template Selector Logic ---
    var templateSelector = $('#template-selector-select');
    if (rifePgAdmin.selectedTemplate) {
        templateSelector.val(rifePgAdmin.selectedTemplate);
    }

    // --- Repeater Logic ---
    $(document).on('click', '.add-repeater-item', function() {
        var repeaterField = $(this).closest('.repeater-field');
        var template = repeaterField.siblings('.repeater-item-template').html();
        var itemsContainer = repeaterField.find('.repeater-items');
        var index = itemsContainer.children().length;
        var newItem = template.replace(/__INDEX__/g, index);
        itemsContainer.append(newItem);
    });

    $(document).on('click', '.remove-repeater-item', function() {
        $(this).closest('.repeater-item').remove();
    });

    // --- Form Submission Logic ---
    $('#page-generator-form').on('submit', function(e) {
        e.preventDefault();
        generatePage();
    });

    // --- Bulk Generate Logic ---
    $('body').on('click', '#bulk-generate-button', function() {
        bulkGeneratePages();
    });

    function generatePage() {
        var formData = collectFormData();

        if (!validateFormData(formData)) {
            return;
        }

        $('#generate-page').addClass('loading');

        $.ajax({
            url: rifePgAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'rife_pg_generate_page',
                nonce: rifePgAdmin.nonce,
                template_id: formData.template_id,
                auto_publish: formData.auto_publish,
                form_data: formData
            },
            success: function(response) {
                $('#generate-page').removeClass('loading');
                if (response.success) {
                    alert('Page generated successfully!\nID: ' + response.data.page_id + '\nURL: ' + response.data.page_url);
                } else {
                    alert('Error: ' + response.data);
                }
            },
            error: function() {
                $('#generate-page').removeClass('loading');
                alert('An unknown error occurred.');
            }
        });
    }

    function bulkGeneratePages() {
        var button = $('#bulk-generate-button');
        var statusMessage = $('#bulk-status-message-header');
        var fileInput = $('#bulk-json-file')[0];

        if (!fileInput.files || fileInput.files.length === 0) {
            statusMessage.html('<div class="notice notice-error"><p>Please select a JSON file.</p></div>');
            return;
        }

        var file = fileInput.files[0];
        if (file.type !== 'application/json') {
            statusMessage.html('<div class="notice notice-error"><p>Error: File must be a valid .json file.</p></div>');
            return;
        }

        var collectedData = collectFormData();
        if (!collectedData.template_id) {
            statusMessage.html('<div class="notice notice-error"><p>Error: No template selected. Please load a template first.</p></div>');
            return;
        }

        button.addClass('loading').prop('disabled', true);
        statusMessage.html('<div class="notice notice-info"><p>Processing... This may take a moment.</p></div>');

        var ajaxData = new FormData();
        ajaxData.append('action', 'rife_pg_bulk_generate_ajax');
        ajaxData.append('nonce', rifePgAdmin.nonce);
        ajaxData.append('bulk_file', file);
        ajaxData.append('template_id', collectedData.template_id);
        ajaxData.append('auto_publish', $('#auto_publish_bulk').is(':checked'));
        ajaxData.append('styles', JSON.stringify(collectedData.styles));

        $.ajax({
            url: rifePgAdmin.ajaxUrl,
            type: 'POST',
            data: ajaxData,
            processData: false,
            contentType: false,
            success: function(response) {
                button.removeClass('loading').prop('disabled', false);
                console.log('Bulk generate response:', response); // Debug log
                
                // Check if response has the expected structure
                if (!response) {
                    statusMessage.html('<div class="notice notice-error"><p>No response received from server.</p></div>');
                    return;
                }
                
                if (response.success) {
                    var successMsg = 'Successfully generated ' + response.data.success_count + ' pages.';
                    if (response.data.error_count > 0) {
                        successMsg += ' ' + response.data.error_count + ' rows failed (e.g., missing hero_title).';
                    }
                    statusMessage.html('<div class="notice notice-success"><p>' + successMsg + '</p></div>');
                } else {
                    // Handle error response
                    var errorMsg = 'An error occurred during bulk generation.';
                    if (response.data) {
                        if (typeof response.data === 'string') {
                            errorMsg = 'Error: ' + response.data;
                        } else if (response.data.message) {
                            errorMsg = 'Error: ' + response.data.message;
                        } else if (response.data.errors && Array.isArray(response.data.errors)) {
                            errorMsg = 'Errors:<br>' + response.data.errors.join('<br>');
                        }
                    }
                    statusMessage.html('<div class="notice notice-error" style="white-space: normal;"><p>' + errorMsg + '</p></div>');
                }
            },
            error: function(xhr, status, error) {
                button.removeClass('loading').prop('disabled', false);
                console.error('Bulk generate AJAX error:', xhr, status, error); // Debug log
                
                // Try to parse server response if available
                var errorMsg = 'An unknown error occurred during the AJAX request.';
                if (xhr.responseText) {
                    try {
                        var serverResponse = JSON.parse(xhr.responseText);
                        if (serverResponse && serverResponse.data) {
                            if (typeof serverResponse.data === 'string') {
                                errorMsg = 'Server Error: ' + serverResponse.data;
                            } else if (serverResponse.data.message) {
                                errorMsg = 'Server Error: ' + serverResponse.data.message;
                            }
                        }
                    } catch (parseError) {
                        // If we can't parse the response, show raw response
                        errorMsg = 'Server Error: ' + xhr.responseText.substring(0, 200) + '...';
                    }
                } else if (xhr.status) {
                    errorMsg = 'HTTP Error ' + xhr.status + ': ' + (xhr.statusText || 'Unknown error');
                }
                
                statusMessage.html('<div class="notice notice-error" style="white-space: normal;"><p>' + errorMsg + '</p></div>');
            }
        });
    }

    function collectFormData() {
        var form = $('#page-generator-form');
        var formData = {
            content: {},
            styles: {},
            template_id: form.find('input[name="template_id"]').val(),
            auto_publish: $('#auto_publish_single').is(':checked') // Use the single publish checkbox
        };

        // Collect content data
        form.find('[name^="content["]').each(function() {
            var name = $(this).attr('name');
            var value = $(this).val();

            // Parse content[section][field] format
            var matches = name.match(/content\[([^\]]+)\]\[([^\]]+)\]/);
            if (matches && matches.length === 3) {
                var section = matches[1];
                var field = matches[2];

                if (!formData.content[section]) {
                    formData.content[section] = {};
                }
                formData.content[section][field] = value;
            }
        });

        // Collect style data
        $('.design-controls input, .design-controls select').each(function() {
            var fieldId = $(this).attr('id') || $(this).attr('name');
            if (fieldId) {
                formData.styles[fieldId] = $(this).val();
            }
        });

        return formData;
    }

    function validateFormData(formData) {
        if (!formData.template_id) {
            alert('Error: No template selected.');
            return false;
        }
        if (!formData.content['hero'] || !formData.content['hero']['title']) {
            alert('Error: Hero Title is required.');
            return false;
        }
        return true;
    }

    // --- AI Prompt Generator Logic (Revised with Yoast SEO & Images) ---
    const seoComprehensiveKeys = ["hero_title", "hero_subtitle", "hero_cta_text", "hero_cta_link", "benefit_1_title", "benefit_1_desc", "benefit_2_title", "benefit_2_desc", "benefit_3_title", "benefit_3_desc", "benefit_4_title", "benefit_4_desc", "benefit_5_title", "benefit_5_desc", "benefit_6_title", "benefit_6_desc", "testimonial_1_name", "testimonial_1_text", "testimonial_2_name", "testimonial_2_text", "testimonial_3_name", "testimonial_3_text", "testimonial_4_name", "testimonial_4_text", "testimonial_5_name", "testimonial_5_text", "testimonial_6_name", "testimonial_6_text", "plan_1_title", "plan_1_price", "plan_1_features", "plan_2_title", "plan_2_price", "plan_2_features", "plan_3_title", "plan_3_price", "plan_3_features", "process_title", "process_subtitle", "process_1_title", "process_1_description", "process_2_title", "process_2_description", "process_3_title", "process_3_description", "process_4_title", "process_4_description", "process_5_title", "process_5_description", "process_6_title", "process_6_description", "pricing_title", "pricing_subtitle", "pricing_1_name", "pricing_1_price", "pricing_1_period", "pricing_1_feature_1", "pricing_1_feature_2", "pricing_1_feature_3", "pricing_1_feature_4", "pricing_1_feature_5", "pricing_1_feature_6", "pricing_1_cta_text", "pricing_1_cta_link", "pricing_2_name", "pricing_2_badge", "pricing_2_price", "pricing_2_period", "pricing_2_feature_1", "pricing_2_feature_2", "pricing_2_feature_3", "pricing_2_feature_4", "pricing_2_feature_5", "pricing_2_feature_6", "pricing_2_feature_7", "pricing_2_feature_8", "pricing_2_cta_text", "pricing_2_cta_link", "pricing_3_name", "pricing_3_price", "pricing_3_period", "pricing_3_feature_1", "pricing_3_feature_2", "pricing_3_feature_3", "pricing_3_feature_4", "pricing_3_feature_5", "pricing_3_feature_6", "pricing_3_feature_7", "pricing_3_feature_8", "pricing_3_feature_9", "pricing_3_cta_text", "pricing_3_cta_link", "pricing_consultation_text", "pricing_consultation_cta", "pricing_consultation_link", "faq_1_question", "faq_1_answer", "faq_2_question", "faq_2_answer", "faq_3_question", "faq_3_answer", "faq_4_question", "faq_4_answer", "faq_5_question", "faq_5_answer", "faq_6_question", "faq_6_answer", "faq_7_question", "faq_7_answer", "faq_8_question", "faq_8_answer", "faq_9_question", "faq_9_answer", "faq_10_question", "faq_10_answer", "study_1_title", "study_1_desc", "study_2_title", "study_2_desc", "study_3_title", "study_3_desc", "guarantees_title", "guarantees_subtitle", "guarantees_1_icon", "guarantees_1_title", "guarantee_1_item_1_title", "guarantee_1_item_1_desc", "guarantee_1_item_2_title", "guarantee_1_item_2_desc", "guarantee_1_item_3_title", "guarantee_1_item_3_desc", "guarantee_1_item_4_title", "guarantee_1_item_4_desc", "guarantee_1_item_5_title", "guarantee_1_item_5_desc", "guarantee_1_item_6_title", "guarantee_1_item_6_desc", "guarantees_2_icon", "guarantees_2_title", "guarantee_2_item_1_title", "guarantee_2_item_1_desc", "guarantee_2_item_2_title", "guarantee_2_item_2_desc", "guarantee_2_item_3_title", "guarantee_2_item_3_desc", "guarantee_2_item_4_title", "guarantee_2_item_4_desc", "guarantee_2_item_5_title", "guarantee_2_item_5_desc", "guarantee_2_item_6_title", "guarantee_2_item_6_desc", "guide_title", "guide_subtitle", "guide_1_icon", "guide_1_title", "guide_1_item_1_title", "guide_1_item_1_desc", "guide_1_item_2_title", "guide_1_item_2_desc", "guide_1_item_3_title", "guide_1_item_3_desc", "guide_1_item_4_title", "guide_1_item_4_desc", "guide_1_item_5_title", "guide_1_item_5_desc", "guide_2_icon", "guide_2_title", "guide_2_item_1_title", "guide_2_item_1_desc", "guide_2_item_2_title", "guide_2_item_2_desc", "guide_2_item_3_title", "guide_2_item_3_desc", "guide_2_item_4_title", "guide_2_item_4_desc", "guide_2_item_5_title", "guide_2_item_5_desc", "guide_cta_text", "guide_cta_description", "guide_cta_button", "guide_cta_icon", "guide_cta_link", "locations_title", "locations_subtitle", "location_1_icon", "location_1_title", "location_1_description", "location_1_area_1", "location_1_area_2", "location_1_area_3", "location_1_area_4", "location_2_icon", "location_2_title", "location_2_description", "location_2_area_1", "location_2_area_2", "location_2_area_3", "location_2_area_4", "location_3_icon", "location_3_title", "location_3_description", "location_3_area_1", "location_3_area_2", "location_3_area_3", "location_3_area_4", "about_title", "about_subtitle", "about_description", "about_stats_1_number", "about_stats_1_label", "about_stats_2_number", "about_stats_2_label", "about_stats_3_number", "about_stats_3_label", "about_stats_4_number", "about_stats_4_label", "contact_email", "contact_phone", "contact_address", "final_cta_title", "final_cta_subtitle", "final_cta_button_text", "final_cta_button_link", "yoast_focus_keyphrase", "yoast_seo_title", "yoast_meta_description", "hero_image_alt", "benefit_1_image_alt", "benefit_2_image_alt", "benefit_3_image_alt", "benefit_4_image_alt", "benefit_5_image_alt", "benefit_6_image_alt"];

    function generatePrompt(keywords, domain, contact) {
        const keywordList = keywords.map(k => `- ${k}`).join('\n');
        const keysString = seoComprehensiveKeys.map(k => `"${k}"`).join(", ");

        // Process domain and contact info
        const websiteDomain = domain ? domain.replace(/^https?:\/\//, '').replace(/\/$/, '') : 'example.com';
        const contactInfo = contact || 'Phone: +62 812-3456-7890\nEmail: <EMAIL>\nAddress: Jakarta, Indonesia';

        const exampleJson = `  {
` + seoComprehensiveKeys.map(k => {
    if (k === 'yoast_focus_keyphrase') {
        return `    "${k}": "${keywords[0] || 'keyword'}"`;
    } else if (k === 'yoast_seo_title') {
        return `    "${k}": "Jasa Pembuatan Website ${keywords[0] || 'Profesional'}: Murah, Cepat & SEO Friendly"`;
    } else if (k === 'yoast_meta_description') {
        return `    "${k}": "Jasa pembuatan website ${keywords[0] || 'profesional'} dengan harga terjangkau. Gratis domain & hosting, support 24/7, garansi maintenance seumur hidup. Hubungi kami sekarang!"`;
    } else if (k === 'hero_title') {
        return `    "${k}": "Jasa Pembuatan Website ${keywords[0] || 'Profesional'}: Murah, Cepat & SEO Friendly"`;
    } else if (k === 'hero_subtitle') {
        return `    "${k}": "Solusi website ${keywords[0] || 'profesional'} untuk UKM, startup, dan company profile. Desain premium, gratis domain & hosting, support 24/7. Dipercaya 500+ klien."`;
    } else if (k === 'benefits_title') {
        return `    "${k}": "Mengapa Memilih Jasa Website ${keywords[0] || 'Profesional'} Kami?"`;
    } else if (k === 'benefits_subtitle') {
        return `    "${k}": "Kami menawarkan solusi website ${keywords[0] || 'profesional'} berkualitas tinggi dengan harga terjangkau dan layanan terbaik"`;
    } else if (k === 'hero_image_alt') {
        return `    "${k}": "jasa pembuatan website ${keywords[0] || 'profesional'} tim ahli"`;
    } else if (k.includes('benefit_') && k.includes('_image_alt')) {
        const benefitNum = k.match(/benefit_(\d+)_image_alt/);
        return `    "${k}": "keunggulan website ${keywords[0] || 'profesional'} ${benefitNum ? benefitNum[1] : '1'}"`;
    } else {
        return `    "${k}": "... (Konten untuk Website ${keywords[0] || 'Profesional'}) ..."`;
    }
}).join(",\n") + `
  }`;

        return `You are an expert copywriter specializing in website development services and SEO optimization for web agencies.

Your task is to generate a JSON array containing ${keywords.length} unique objects. Each object will provide the complete text content for a professional website development service landing page based on one of the following keywords:

${keywordList}

BUSINESS CONTEXT: Professional Website Development Services
- Target Audience: UKM, startup, company profile, e-commerce businesses
- Service Focus: Website creation, responsive design, SEO-friendly development
- Unique Selling Points: Affordable pricing, fast delivery (3-7 days), 24/7 support, lifetime maintenance guarantee
- Pricing Tiers: Basic (Rp 730k), Full Service (Rp 1.7M), Business (Rp 4M)

WEBSITE INFORMATION:
- Domain: ${websiteDomain}
- Contact Information: ${contactInfo.replace(/\n/g, ' | ')}
- WhatsApp: +6285226272923

For each object, create compelling, SEO-optimized content specifically for website development services that includes proper Yoast SEO fields.

CRITICAL: Each JSON object must have the following keys, exactly as specified. Do not add, remove, or change any keys.

Required Keys:
${keysString}

ADVANCED SEO OPTIMIZATION INSTRUCTIONS (SOLVE YOAST SEO PROBLEMS):

1. KEYPHRASE DISTRIBUTION (SOLVE: "Keyphrase distribution"):
- Naturally distribute the focus keyphrase throughout ALL content sections
- Include "jasa pembuatan website" + keyphrase in: hero_title, hero_subtitle, benefits_title, process_title, pricing_title, faq_title
- Focus on website development terminology: "website profesional", "desain responsif", "SEO friendly", "maintenance"
- Use variations: "jasa website", "pembuatan website", "layanan website", "website berkualitas"

2. KEYPHRASE IN SUBHEADINGS (SOLVE: "Keyphrase in subheading"):
- MANDATORY: Include website development keywords in ALL H2 and H3 subheadings:
  - benefits_title: "Mengapa Memilih Jasa Website [keyphrase] Kami?"
  - process_title: "Alur Kerja Pembuatan Website [keyphrase]"
  - pricing_title: "Paket Harga Website [keyphrase] Terjangkau"
  - faq_title: "FAQ Seputar Jasa Website [keyphrase]"
  - testimonials_title: "Testimoni Klien Website [keyphrase]"
- Focus on website benefits: kredibilitas, penjualan, SEO, responsif

3. INTERNAL LINKS (SOLVE: "Internal links"):
- Include internal links in content using the provided domain (${websiteDomain})
- Add internal links in hero_subtitle, benefit descriptions, and process steps
- Link to related services using slug format: https://${websiteDomain}/[keyword-slug]
- Example internal links:
  ${keywords.map(k => `  - https://${websiteDomain}/${k.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')}`).join('\n')}
- Include links to current page and related services naturally in the content
- Add contact page link: https://${websiteDomain}/contact

4. CONTACT INFORMATION CONSISTENCY:
- Use WhatsApp: +6285226272923 for all CTA buttons and contact sections
- Include "Konsultasi Gratis via WhatsApp" as primary CTA
- Format: ${contactInfo.replace(/\n/g, ' | ')}
- Always mention "jasamurahweb.com" as the brand

5. IMAGE ALT TEXTS:
- "hero_image_alt": "jasa pembuatan website [keyphrase] profesional tim ahli"
- "benefit_X_image_alt": "keunggulan website [keyphrase] - [benefit description]"
- Focus on website development visuals and professional team

6. YOAST SEO FIELDS:
- "yoast_focus_keyphrase": Use "jasa pembuatan website [keyword]" format
- "yoast_seo_title": "Jasa Pembuatan Website [Keyword]: Murah, Cepat & SEO Friendly" (under 60 chars)
- "yoast_meta_description": Include pricing, benefits, and WhatsApp contact (under 160 chars)

7. WEBSITE DEVELOPMENT FOCUS:
- Emphasize: responsive design, SEO friendly, fast loading, mobile-first
- Pricing: Basic (730k), Full Service (1.7M), Business (4M)
- Timeline: 3-7 days completion
- Guarantees: lifetime maintenance, 24/7 support
- Target: UKM, startup, company profile, e-commerce

Example JSON Structure:
[
${exampleJson},
  {
    "hero_title": "Jasa Pembuatan Website ${keywords[1] || 'Berkualitas'}: Murah, Cepat & SEO Friendly",
    "hero_subtitle": "Solusi website ${keywords[1] || 'berkualitas'} untuk UKM dan startup. Desain premium, gratis domain & hosting, support 24/7. Dipercaya 500+ klien Indonesia.",
    "benefits_title": "Mengapa Memilih Jasa Website ${keywords[1] || 'Berkualitas'} Kami?",
    "benefits_subtitle": "Kami menawarkan solusi website ${keywords[1] || 'berkualitas'} dengan harga terjangkau dan layanan terbaik di Indonesia",
    "hero_image_alt": "jasa pembuatan website ${keywords[1] || 'berkualitas'} tim profesional",
    "benefit_1_image_alt": "keunggulan website ${keywords[1] || 'berkualitas'} - kredibilitas bisnis",
    "benefit_2_image_alt": "keunggulan website ${keywords[1] || 'berkualitas'} - jangkauan pasar luas",
    "yoast_focus_keyphrase": "jasa pembuatan website ${keywords[1] || 'berkualitas'}",
    "yoast_seo_title": "Jasa Website ${keywords[1] || 'Berkualitas'}: Murah, Cepat & SEO Friendly",
    "yoast_meta_description": "Jasa pembuatan website ${keywords[1] || 'berkualitas'} mulai 730rb. Gratis domain & hosting, support 24/7. WhatsApp: 085226272923",
    // ... dan seterusnya untuk semua field ...
  }
  // ... etc. for all ${keywords.length} keywords ...
]`;
    }

    $('.generate-prompt-button').on('click', function() {
        var templateId = $(this).data('template-id');
        if (templateId === 'seo-comprehensive') {
            $('#ai-prompt-modal').show();
            $('#prompt-keywords').val('');
            $('#prompt-domain').val('');
            $('#prompt-contact').val('');
            $('#prompt-keywords').trigger('change');
        }
    });

    $('#ai-prompt-modal .modal-close, #ai-prompt-modal .modal-overlay').on('click', function() {
        $('#ai-prompt-modal').hide();
    });

    $('#prompt-keywords, #prompt-domain, #prompt-contact').on('keyup change', function() {
        const keywords = $('#prompt-keywords').val().split('\n').map(k => k.trim()).filter(k => k);
        const domain = $('#prompt-domain').val().trim();
        const contact = $('#prompt-contact').val().trim();

        if (keywords.length === 0) {
            keywords.push('[YOUR KEYWORD HERE]');
        }
        const prompt = generatePrompt(keywords, domain, contact);
        $('#ai-prompt-output').val(prompt);
    });

    $('.copy-prompt-button').on('click', function() {
        var promptText = document.getElementById('ai-prompt-output');
        promptText.select();
        promptText.setSelectionRange(0, 99999); // For mobile devices
        document.execCommand('copy');

        var feedback = $('#copy-prompt-feedback');
        var button = $(this);
        var originalText = button.html();

        feedback.show();
        button.html('Copied!');

        setTimeout(function() {
            feedback.hide();
            button.html(originalText);
        }, 2000);
    });
    // Masonry layout initialization - Simplified version
    function initMasonryLayout() {
        const container = $('.content-sections-container');
        if (!container.length) return;

        // Add masonry-layout class to enable CSS Grid masonry
        container.addClass('masonry-layout');

        // Monitor for DOM changes to maintain layout
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'subtree') {
                    // Just ensure the class is still there
                    container.addClass('masonry-layout');
                }
            });
        });

        observer.observe(container[0], {
            childList: true,
            subtree: true,
            attributes: false // No need to monitor attributes
        });
    }

    // Initialize masonry when template is loaded
    if ($('.content-sections-container').length) {
        initMasonryLayout();
    }

    // Re-initialize when template changes
    $(document).on('template-loaded', function() {
        setTimeout(initMasonryLayout, 100);
    });
});