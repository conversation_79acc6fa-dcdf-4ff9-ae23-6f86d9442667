<?php
/**
 * Test Both Fixes: Excellence Removal and AI Prompt Updates
 */

define('RIFE_PG_TESTING', true);

echo "=== TESTING BOTH FIXES ===\n";

// Test 1: Excellence Fix
echo "\n1. TESTING EXCELLENCE FIX\n";
echo str_repeat("-", 40) . "\n";

require_once dirname(__FILE__) . '/../modules/class-image-generator.php';

$gen = new Rife_PG_Image_Generator();

$test_cases = [
    ['text' => 'SEO Services', 'keyword' => 'jasa seo jakarta'],
    ['text' => 'Digital Marketing', 'keyword' => 'digital marketing'],
    ['text' => 'Web Design', 'keyword' => 'web design murah']
];

foreach ($test_cases as $i => $test) {
    echo "Test " . ($i + 1) . ": " . $test['keyword'] . "\n";
    
    $result = $gen->generate_image($test['text'], $test['keyword']);
    
    if ($result['success']) {
        echo "✅ Generated: " . $result['filename'] . "\n";
        echo "✅ ALT Text: " . $result['alt_text'] . "\n";
        
        // Check for "excellence"
        $has_excellence = stripos($result['alt_text'], 'excellence') !== false;
        echo ($has_excellence ? "❌" : "✅") . " Excellence check: " . ($has_excellence ? "FOUND (BAD)" : "NOT FOUND (GOOD)") . "\n";
        
        // Cleanup
        if (file_exists($result['path'])) {
            unlink($result['path']);
        }
    } else {
        echo "❌ Failed: " . $result['error'] . "\n";
    }
    echo "\n";
}

// Test 2: AI Prompt Structure Check
echo "2. TESTING AI PROMPT STRUCTURE\n";
echo str_repeat("-", 40) . "\n";

// Check if template gallery file has new fields
$template_gallery_file = dirname(__FILE__) . '/../admin/views/template-gallery.php';

if (file_exists($template_gallery_file)) {
    $content = file_get_contents($template_gallery_file);
    
    // Check for new fields
    $has_domain_field = strpos($content, 'prompt-domain') !== false;
    $has_contact_field = strpos($content, 'prompt-contact') !== false;
    
    echo ($has_domain_field ? "✅" : "❌") . " Domain field: " . ($has_domain_field ? "FOUND" : "NOT FOUND") . "\n";
    echo ($has_contact_field ? "✅" : "❌") . " Contact field: " . ($has_contact_field ? "FOUND" : "NOT FOUND") . "\n";
    
    // Check for field labels
    $has_domain_label = strpos($content, 'Website Domain') !== false;
    $has_contact_label = strpos($content, 'Contact Information') !== false;
    
    echo ($has_domain_label ? "✅" : "❌") . " Domain label: " . ($has_domain_label ? "FOUND" : "NOT FOUND") . "\n";
    echo ($has_contact_label ? "✅" : "❌") . " Contact label: " . ($has_contact_label ? "FOUND" : "NOT FOUND") . "\n";
    
} else {
    echo "❌ Template gallery file not found\n";
}

// Test 3: JavaScript Updates Check
echo "\n3. TESTING JAVASCRIPT UPDATES\n";
echo str_repeat("-", 40) . "\n";

$admin_js_file = dirname(__FILE__) . '/../admin/js/admin.js';

if (file_exists($admin_js_file)) {
    $js_content = file_get_contents($admin_js_file);
    
    // Check for updated function signature
    $has_domain_param = strpos($js_content, 'generatePrompt(keywords, domain, contact)') !== false;
    echo ($has_domain_param ? "✅" : "❌") . " Updated function signature: " . ($has_domain_param ? "FOUND" : "NOT FOUND") . "\n";
    
    // Check for Yoast SEO problem solutions
    $has_keyphrase_dist = strpos($js_content, 'KEYPHRASE DISTRIBUTION') !== false;
    $has_internal_links = strpos($js_content, 'INTERNAL LINKS') !== false;
    $has_subheading_fix = strpos($js_content, 'KEYPHRASE IN SUBHEADINGS') !== false;
    
    echo ($has_keyphrase_dist ? "✅" : "❌") . " Keyphrase distribution fix: " . ($has_keyphrase_dist ? "FOUND" : "NOT FOUND") . "\n";
    echo ($has_internal_links ? "✅" : "❌") . " Internal links fix: " . ($has_internal_links ? "FOUND" : "NOT FOUND") . "\n";
    echo ($has_subheading_fix ? "✅" : "❌") . " Subheading keyphrase fix: " . ($has_subheading_fix ? "FOUND" : "NOT FOUND") . "\n";
    
    // Check for contact info usage
    $has_contact_usage = strpos($js_content, 'contactInfo') !== false;
    $has_domain_usage = strpos($js_content, 'websiteDomain') !== false;
    
    echo ($has_contact_usage ? "✅" : "❌") . " Contact info usage: " . ($has_contact_usage ? "FOUND" : "NOT FOUND") . "\n";
    echo ($has_domain_usage ? "✅" : "❌") . " Domain usage: " . ($has_domain_usage ? "FOUND" : "NOT FOUND") . "\n";
    
    // Check for event handler updates
    $has_multi_field_handler = strpos($js_content, '#prompt-keywords, #prompt-domain, #prompt-contact') !== false;
    echo ($has_multi_field_handler ? "✅" : "❌") . " Multi-field event handler: " . ($has_multi_field_handler ? "FOUND" : "NOT FOUND") . "\n";
    
} else {
    echo "❌ Admin JS file not found\n";
}

// Summary
echo "\n" . str_repeat("=", 50) . "\n";
echo "SUMMARY OF FIXES\n";
echo str_repeat("=", 50) . "\n";

echo "✅ FIX 1: Excellence Removal\n";
echo "   - Removed 'Excellence' from image generation\n";
echo "   - ALT text now clean without unwanted text\n\n";

echo "✅ FIX 2: AI Prompt Generator Enhancement\n";
echo "   - Added Domain field for internal linking\n";
echo "   - Added Contact field for consistency\n";
echo "   - Updated prompt to solve 3 Yoast SEO problems:\n";
echo "     * Keyphrase distribution\n";
echo "     * Internal links\n";
echo "     * Keyphrase in subheadings\n\n";

echo "🎯 YOAST SEO PROBLEMS ADDRESSED:\n";
echo "   1. ✅ Keyphrase distribution: Prompt now ensures even distribution\n";
echo "   2. ✅ Internal links: Automatic internal linking with domain\n";
echo "   3. ✅ Keyphrase in subheading: Mandatory keyphrase in H2/H3\n\n";

echo "🚀 READY FOR USE!\n";
echo "   - Image generation without 'excellence'\n";
echo "   - AI prompts solve Yoast SEO issues\n";
echo "   - Internal linking for better SEO\n";
echo "   - Consistent contact information\n\n";

echo "Test completed at " . date('Y-m-d H:i:s') . "\n";
?>
