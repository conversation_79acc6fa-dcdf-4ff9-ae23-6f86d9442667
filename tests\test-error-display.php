<?php
/**
 * Test with Full Error Display
 * 
 * This test displays any errors in full detail to help with debugging.
 */

// Enable comprehensive error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "=== FULL ERROR DISPLAY TEST ===\n";
echo "Testing image generation with complete error reporting...\n\n";

// Custom error handler to capture and display all errors
function display_error($errno, $errstr, $errfile, $errline) {
    $error_types = [
        E_ERROR => 'FATAL ERROR',
        E_WARNING => 'WARNING', 
        E_PARSE => 'PARSE ERROR',
        E_NOTICE => 'NOTICE',
        E_CORE_ERROR => 'CORE ERROR',
        E_CORE_WARNING => 'CORE WARNING',
        E_COMPILE_ERROR => 'COMPILE ERROR',
        E_COMPILE_WARNING => 'COMPILE WARNING',
        E_USER_ERROR => 'USER ERROR',
        E_USER_WARNING => 'USER WARNING',
        E_USER_NOTICE => 'USER NOTICE',
        E_STRICT => 'STRICT NOTICE',
        E_RECOVERABLE_ERROR => 'RECOVERABLE ERROR',
        E_DEPRECATED => 'DEPRECATED',
        E_USER_DEPRECATED => 'USER DEPRECATED'
    ];
    
    $error_type = isset($error_types[$errno]) ? $error_types[$errno] : 'UNKNOWN ERROR';
    
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "🚨 $error_type\n";
    echo str_repeat("=", 80) . "\n";
    echo "MESSAGE: $errstr\n";
    echo "FILE: $errfile\n";
    echo "LINE: $errline\n";
    echo "ERROR CODE: $errno\n";
    echo str_repeat("=", 80) . "\n\n";
    
    // Don't stop execution for warnings and notices
    if ($errno == E_ERROR || $errno == E_CORE_ERROR || $errno == E_COMPILE_ERROR || $errno == E_USER_ERROR) {
        die("FATAL ERROR OCCURRED. EXECUTION STOPPED.\n");
    }
    
    return true;
}

// Set custom error handler
set_error_handler('display_error');

// Custom exception handler
function display_exception($exception) {
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "🚨 UNCAUGHT EXCEPTION\n";
    echo str_repeat("=", 80) . "\n";
    echo "MESSAGE: " . $exception->getMessage() . "\n";
    echo "FILE: " . $exception->getFile() . "\n";
    echo "LINE: " . $exception->getLine() . "\n";
    echo "CODE: " . $exception->getCode() . "\n";
    echo "\nSTACK TRACE:\n";
    echo $exception->getTraceAsString() . "\n";
    echo str_repeat("=", 80) . "\n\n";
}

set_exception_handler('display_exception');

echo "1. ENVIRONMENT CHECK\n";
echo str_repeat("-", 40) . "\n";

echo "PHP Version: " . phpversion() . "\n";

$extensions = ['gd', 'json'];
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? 'AVAILABLE' : 'MISSING';
    echo "$ext Extension: $status\n";
}

if (extension_loaded('gd')) {
    $gd_info = gd_info();
    echo "GD Version: " . $gd_info['GD Version'] . "\n";
    echo "JPEG Support: " . ($gd_info['JPEG Support'] ? 'YES' : 'NO') . "\n";
}

echo "\n2. FILE STRUCTURE CHECK\n";
echo str_repeat("-", 40) . "\n";

$files = [
    '../modules/class-image-generator.php' => 'Image Generator',
    '../admin/views/example-with-yoast.json' => 'Example JSON'
];

foreach ($files as $file => $desc) {
    $path = dirname(__FILE__) . '/' . $file;
    $status = file_exists($path) ? 'EXISTS' : 'MISSING';
    echo "$desc: $status\n";
}

echo "\n3. IMAGE GENERATOR TEST\n";
echo str_repeat("-", 40) . "\n";

try {
    echo "Loading Image Generator class...\n";
    require_once dirname(__FILE__) . '/../modules/class-image-generator.php';
    
    if (class_exists('Rife_PG_Image_Generator')) {
        echo "✅ Image Generator class loaded\n";
        
        echo "Creating instance...\n";
        $generator = new Rife_PG_Image_Generator();
        echo "✅ Instance created\n";
        
        echo "Testing image generation...\n";
        
        // Test case that previously caused error
        $result = $generator->generate_image('Test SEO Services', 'seo jakarta');
        
        if ($result['success']) {
            echo "✅ IMAGE GENERATION SUCCESS!\n";
            echo "Filename: " . $result['filename'] . "\n";
            echo "ALT Text: " . $result['alt_text'] . "\n";
            
            if (isset($result['path']) && file_exists($result['path'])) {
                $size = filesize($result['path']);
                echo "File Size: " . number_format($size) . " bytes\n";
                
                // Cleanup
                unlink($result['path']);
                echo "✅ Test file cleaned up\n";
            }
        } else {
            echo "❌ IMAGE GENERATION FAILED\n";
            echo "Error: " . ($result['error'] ?? 'Unknown error') . "\n";
        }
        
    } else {
        echo "❌ Image Generator class not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ EXCEPTION CAUGHT\n";
    echo "Message: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

echo "\n4. YOAST SEO DATA TEST\n";
echo str_repeat("-", 40) . "\n";

$json_file = dirname(__FILE__) . '/../admin/views/example-with-yoast.json';

if (file_exists($json_file)) {
    echo "✅ JSON file found\n";
    
    $content = file_get_contents($json_file);
    $data = json_decode($content, true);
    
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ JSON parsed successfully\n";
        echo "Items: " . count($data) . "\n";
        
        if (!empty($data[0])) {
            $first = $data[0];
            $fields = ['yoast_focus_keyphrase', 'yoast_seo_title', 'yoast_meta_description'];
            
            foreach ($fields as $field) {
                if (isset($first[$field]) && !empty($first[$field])) {
                    $value = substr($first[$field], 0, 50) . '...';
                    echo "✅ $field: $value\n";
                } else {
                    echo "❌ $field: MISSING\n";
                }
            }
        }
    } else {
        echo "❌ JSON parsing failed: " . json_last_error_msg() . "\n";
    }
} else {
    echo "❌ JSON file not found\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "TEST COMPLETED\n";
echo "If any errors occurred, they are displayed above in full detail.\n";
echo str_repeat("=", 50) . "\n";

// Restore handlers
restore_error_handler();
restore_exception_handler();
?>
