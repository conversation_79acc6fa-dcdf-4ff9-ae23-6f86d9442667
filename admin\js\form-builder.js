/**
 * Form Builder JavaScript for Rife PageGenerator
 * Handles dynamic form sections and repeater fields
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Form section management
    var FormBuilder = {
        
        // Initialize form builder
        init: function() {
            this.bindEvents();
            this.initializeRepeaters();
            this.setupFormValidation();
        },
        
        // Bind events
        bindEvents: function() {
            // Template change handler
            $('#template-selector').on('change', this.handleTemplateChange.bind(this));
            
            // Repeater field handlers
            $(document).on('click', '.add-repeater-item', this.addRepeaterItem.bind(this));
            $(document).on('click', '.remove-repeater-item', this.removeRepeaterItem.bind(this));
            
            // Form section navigation
            $(document).on('click', '.section-nav-btn', this.navigateToSection.bind(this));
            
            // Real-time validation
            $(document).on('blur', 'input[required], textarea[required]', this.validateField.bind(this));
            
            // Character counter for text fields
            $(document).on('input', 'input[data-max-length], textarea[data-max-length]', this.updateCharacterCounter.bind(this));
            
        },
        
        // Handle template selection change
        handleTemplateChange: function(e) {
            var templateId = $(e.target).val();
            var selectedOption = $(e.target).find(':selected');
            var sections = selectedOption.data('sections');
            
            if (templateId && sections) {
                this.showTemplateSections(sections);
                this.updateFormProgress();
                this.enableNextSection();
            } else {
                this.hideAllSections();
            }
        },
        
        // Show template-specific sections
        showTemplateSections: function(sections) {
            // Hide all sections first
            $('.content-section').hide().removeClass('required-section');
            
            // Show only relevant sections
            if (sections && sections.length > 0) {
                sections.forEach(function(section) {
                    var sectionElement = $('.content-section[data-section="' + section + '"]');
                    sectionElement.show().addClass('required-section');
                    
                    // Mark required fields
                    sectionElement.find('input, textarea').each(function() {
                        var field = $(this);
                        var fieldName = field.attr('name');
                        
                        // Hero title is always required
                        if (section === 'hero' && fieldName && fieldName.includes('[title]')) {
                            field.attr('required', true);
                        }
                    });
                });
                
                // Show content input section
                $('.content-input').show();
                this.createSectionNavigation(sections);
            }
        },
        
        // Hide all sections
        hideAllSections: function() {
            $('.content-input, .style-customizer, .generation-actions').hide();
            $('.content-section').hide().removeClass('required-section');
        },
        
        // Create section navigation
        createSectionNavigation: function(sections) {
            var navHtml = '<div class="section-navigation mb-4">';
            navHtml += '<div class="nav nav-pills" role="tablist">';
            
            sections.forEach(function(section, index) {
                var sectionTitle = section.charAt(0).toUpperCase() + section.slice(1).replace('-', ' ');
                var activeClass = index === 0 ? 'active' : '';
                
                navHtml += '<button class="nav-link section-nav-btn ' + activeClass + '" ';
                navHtml += 'data-section="' + section + '" type="button">';
                navHtml += '<span class="step-number">' + (index + 1) + '</span>';
                navHtml += '<span class="step-title">' + sectionTitle + '</span>';
                navHtml += '</button>';
            });
            
            navHtml += '</div></div>';
            
            // Insert navigation before content sections
            $('.content-sections').prepend(navHtml);
            
            // Show first section
            this.showSection(sections[0]);
        },
        
        // Navigate to specific section
        navigateToSection: function(e) {
            var sectionId = $(e.target).closest('.section-nav-btn').data('section');
            this.showSection(sectionId);
            
            // Update navigation state
            $('.section-nav-btn').removeClass('active');
            $(e.target).closest('.section-nav-btn').addClass('active');
        },
        
        // Show specific section
        showSection: function(sectionId) {
            $('.content-section').hide();
            $('.content-section[data-section="' + sectionId + '"]').show();
            
            // Scroll to section
            $('html, body').animate({
                scrollTop: $('.content-section[data-section="' + sectionId + '"]').offset().top - 100
            }, 300);
        },
        
        // Initialize repeater fields
        initializeRepeaters: function() {
            $('.repeater-field').each(function() {
                var repeater = $(this);
                var fieldType = repeater.data('field');
                
                // Add initial item if none exist
                if (repeater.find('.repeater-item:not(.template)').length === 0) {
                    // Add one initial item for demonstration
                    // repeater.find('.add-repeater-item').trigger('click');
                }
            });
        },
        
        // Add repeater item
        addRepeaterItem: function(e) {
            e.preventDefault();
            
            var button = $(e.target).closest('.add-repeater-item');
            var repeaterField = button.closest('.repeater-field');
            var template = repeaterField.find('.repeater-item.template');
            var itemsContainer = repeaterField.find('.repeater-items');
            var newItem = template.clone();
            
            // Remove template class and show
            newItem.removeClass('template').show();
            
            // Update field names with proper index
            var index = itemsContainer.find('.repeater-item:not(.template)').length;
            this.updateRepeaterFieldNames(newItem, index);
            
            // Add to container with animation
            newItem.hide().appendTo(itemsContainer).slideDown(300);
            
            // Focus first field
            setTimeout(function() {
                newItem.find('input, textarea').first().focus();
            }, 350);
            
            // Update form progress
            this.updateFormProgress();
        },
        
        // Remove repeater item
        removeRepeaterItem: function(e) {
            e.preventDefault();
            
            var item = $(e.target).closest('.repeater-item');
            var container = item.closest('.repeater-items');
            
            // Animate removal
            item.slideUp(300, function() {
                $(this).remove();
                
                // Re-index remaining items
                container.find('.repeater-item:not(.template)').each(function(index) {
                    FormBuilder.updateRepeaterFieldNames($(this), index);
                });
                
                // Update form progress
                FormBuilder.updateFormProgress();
            });
        },
        
        // Update repeater field names
        updateRepeaterFieldNames: function(item, index) {
            item.find('input, textarea, select').each(function() {
                var field = $(this);
                var name = field.attr('name');
                
                if (name) {
                    // Replace INDEX placeholder with actual index
                    var newName = name.replace(/\[INDEX\]/g, '[' + index + ']');
                    field.attr('name', newName);
                }
            });
        },
        
        // Setup form validation
        setupFormValidation: function() {
            // Add validation classes
            $('input[required], textarea[required], select[required]').addClass('validate-required');
            
            // Add character counters
            $('input[maxlength], textarea[maxlength]').each(function() {
                var field = $(this);
                var maxLength = field.attr('maxlength');
                field.attr('data-max-length', maxLength);
                
                // Add counter element
                var counter = $('<small class="character-counter text-muted"></small>');
                field.after(counter);
                
                // Update counter
                this.updateCharacterCounter.call(field[0]);
            }.bind(this));
        },
        
        // Validate individual field
        validateField: function(e) {
            var field = $(e.target);
            var value = field.val().trim();
            var isValid = true;
            var errorMessage = '';
            
            // Required field validation
            if (field.attr('required') && !value) {
                isValid = false;
                errorMessage = 'This field is required';
            }
            
            // Email validation
            if (field.attr('type') === 'email' && value) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address';
                }
            }
            
            // URL validation
            if (field.attr('type') === 'url' && value) {
                try {
                    new URL(value);
                } catch (e) {
                    isValid = false;
                    errorMessage = 'Please enter a valid URL';
                }
            }
            
            // Update field state
            this.updateFieldValidationState(field, isValid, errorMessage);
            
            return isValid;
        },
        
        // Update field validation state
        updateFieldValidationState: function(field, isValid, errorMessage) {
            var fieldGroup = field.closest('.field-group');
            var existingError = fieldGroup.find('.field-error');
            
            // Remove existing error
            existingError.remove();
            field.removeClass('is-invalid is-valid');
            
            if (isValid) {
                field.addClass('is-valid');
            } else {
                field.addClass('is-invalid');
                
                // Add error message
                if (errorMessage) {
                    var errorElement = $('<div class="field-error text-danger small mt-1"></div>');
                    errorElement.text(errorMessage);
                    field.after(errorElement);
                }
            }
        },
        
        // Update character counter
        updateCharacterCounter: function(e) {
            var field = $(e ? e.target : this);
            var maxLength = field.data('max-length') || field.attr('maxlength');
            var currentLength = field.val().length;
            var counter = field.siblings('.character-counter');
            
            if (counter.length && maxLength) {
                var remaining = maxLength - currentLength;
                var text = currentLength + ' / ' + maxLength;
                
                if (remaining < 0) {
                    text += ' (exceeded)';
                    counter.addClass('text-danger').removeClass('text-muted text-warning');
                } else if (remaining < 20) {
                    counter.addClass('text-warning').removeClass('text-muted text-danger');
                } else {
                    counter.addClass('text-muted').removeClass('text-danger text-warning');
                }
                
                counter.text(text);
            }
        },
        
        
        // Update form progress
        updateFormProgress: function() {
            var totalSections = $('.required-section:visible').length;
            var completedSections = 0;
            
            $('.required-section:visible').each(function() {
                var section = $(this);
                var requiredFields = section.find('input[required], textarea[required]');
                var filledFields = 0;
                
                requiredFields.each(function() {
                    if ($(this).val().trim()) {
                        filledFields++;
                    }
                });
                
                if (filledFields === requiredFields.length && requiredFields.length > 0) {
                    completedSections++;
                }
            });
            
            var progress = totalSections > 0 ? (completedSections / totalSections) * 100 : 0;
            
            // Update progress bar if exists
            $('.form-progress-bar').css('width', progress + '%');
            $('.form-progress-text').text(Math.round(progress) + '% Complete');
            
            // Enable/disable next steps
            if (progress >= 100) {
                this.enableNextSection();
            }
        },
        
        // Enable next section
        enableNextSection: function() {
            $('.style-customizer, .generation-actions').show();
        },
        
        // Get form completion status
        getFormCompletionStatus: function() {
            var status = {
                template_selected: !!$('#template-selector').val(),
                required_fields_filled: true,
                sections_completed: []
            };
            
            $('.required-section:visible').each(function() {
                var section = $(this);
                var sectionId = section.data('section');
                var requiredFields = section.find('input[required], textarea[required]');
                var isComplete = true;
                
                requiredFields.each(function() {
                    if (!$(this).val().trim()) {
                        isComplete = false;
                        status.required_fields_filled = false;
                    }
                });
                
                status.sections_completed.push({
                    section: sectionId,
                    completed: isComplete
                });
            });
            
            return status;
        }
    };
    
    // Initialize form builder
    FormBuilder.init();
    
    // Make FormBuilder available globally
    window.RifePgFormBuilder = FormBuilder;
});
