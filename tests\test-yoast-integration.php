<?php
/**
 * Test file for Yoast SEO Integration
 * 
 * This file tests the integration between Rife PageGenerator and Yoast SEO
 * to ensure all components work correctly together.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing purposes, we'll define ABSPATH if not already defined
    define('ABSPATH', dirname(__FILE__) . '/../../../../');
}

// Include WordPress if not already loaded
if (!function_exists('wp_insert_post')) {
    require_once ABSPATH . 'wp-config.php';
}

// Include our plugin files
require_once dirname(__FILE__) . '/../rife-pagegenerator.php';

class Rife_PG_Yoast_Integration_Test {
    
    private $test_results = [];
    
    public function __construct() {
        echo "<h1>Rife PageGenerator - Yoast SEO Integration Test</h1>\n";
        $this->run_all_tests();
        $this->display_results();
    }
    
    /**
     * Run all integration tests
     */
    private function run_all_tests() {
        $this->test_plugin_loaded();
        $this->test_image_generator_class();
        $this->test_yoast_seo_extraction();
        $this->test_gd_library();
        $this->test_json_structure();
        $this->test_wordpress_functions();
    }
    
    /**
     * Test if plugin is loaded correctly
     */
    private function test_plugin_loaded() {
        $test_name = "Plugin Loading Test";
        
        try {
            if (class_exists('Rife_PageGenerator')) {
                $this->add_result($test_name, true, "Rife_PageGenerator class loaded successfully");
            } else {
                $this->add_result($test_name, false, "Rife_PageGenerator class not found");
                return;
            }
            
            if (class_exists('Rife_PG_Page_Generator')) {
                $this->add_result($test_name, true, "Rife_PG_Page_Generator class loaded successfully");
            } else {
                $this->add_result($test_name, false, "Rife_PG_Page_Generator class not found");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test if Image Generator class is available
     */
    private function test_image_generator_class() {
        $test_name = "Image Generator Class Test";
        
        try {
            if (class_exists('Rife_PG_Image_Generator')) {
                $this->add_result($test_name, true, "Rife_PG_Image_Generator class loaded successfully");
                
                // Test instantiation
                $image_generator = new Rife_PG_Image_Generator();
                if ($image_generator instanceof Rife_PG_Image_Generator) {
                    $this->add_result($test_name, true, "Image generator can be instantiated");
                } else {
                    $this->add_result($test_name, false, "Failed to instantiate image generator");
                }
                
            } else {
                $this->add_result($test_name, false, "Rife_PG_Image_Generator class not found");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test Yoast SEO data extraction
     */
    private function test_yoast_seo_extraction() {
        $test_name = "Yoast SEO Data Extraction Test";
        
        try {
            if (!class_exists('Rife_PG_Page_Generator')) {
                $this->add_result($test_name, false, "Rife_PG_Page_Generator class not available");
                return;
            }
            
            // Create test data
            $test_data = [
                'yoast_focus_keyphrase' => 'test keyword',
                'yoast_seo_title' => 'Test SEO Title',
                'yoast_meta_description' => 'Test meta description for SEO'
            ];
            
            // We need to use reflection to test private method
            $page_generator = new Rife_PG_Page_Generator();
            $reflection = new ReflectionClass($page_generator);
            $method = $reflection->getMethod('extract_yoast_seo_data');
            $method->setAccessible(true);
            
            $result = $method->invoke($page_generator, $test_data);
            
            if (is_array($result) && !empty($result)) {
                $this->add_result($test_name, true, "Yoast SEO data extracted successfully: " . count($result) . " fields");
                
                // Check specific fields
                $expected_fields = [
                    '_yoast_wpseo_focuskw',
                    '_yoast_wpseo_title',
                    '_yoast_wpseo_metadesc'
                ];
                
                foreach ($expected_fields as $field) {
                    if (isset($result[$field])) {
                        $this->add_result($test_name, true, "Field $field extracted correctly: " . $result[$field]);
                    } else {
                        $this->add_result($test_name, false, "Field $field missing from extraction");
                    }
                }
                
            } else {
                $this->add_result($test_name, false, "Failed to extract Yoast SEO data");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test GD Library availability
     */
    private function test_gd_library() {
        $test_name = "GD Library Test";
        
        try {
            if (extension_loaded('gd')) {
                $this->add_result($test_name, true, "GD Library is available");
                
                $gd_info = gd_info();
                $this->add_result($test_name, true, "GD Version: " . $gd_info['GD Version']);
                
                if ($gd_info['JPEG Support']) {
                    $this->add_result($test_name, true, "JPEG support available");
                } else {
                    $this->add_result($test_name, false, "JPEG support not available");
                }
                
            } else {
                $this->add_result($test_name, false, "GD Library not available - will use fallback images");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test JSON structure validation
     */
    private function test_json_structure() {
        $test_name = "JSON Structure Test";
        
        try {
            $json_file = dirname(__FILE__) . '/../admin/views/example-with-yoast.json';
            
            if (file_exists($json_file)) {
                $this->add_result($test_name, true, "Example JSON file exists");
                
                $json_content = file_get_contents($json_file);
                $data = json_decode($json_content, true);
                
                if (json_last_error() === JSON_ERROR_NONE) {
                    $this->add_result($test_name, true, "JSON is valid");
                    
                    if (is_array($data) && !empty($data)) {
                        $this->add_result($test_name, true, "JSON contains " . count($data) . " items");
                        
                        // Check first item for required Yoast fields
                        $first_item = $data[0];
                        $yoast_fields = ['yoast_focus_keyphrase', 'yoast_seo_title', 'yoast_meta_description'];
                        
                        foreach ($yoast_fields as $field) {
                            if (isset($first_item[$field]) && !empty($first_item[$field])) {
                                $this->add_result($test_name, true, "Field $field present: " . substr($first_item[$field], 0, 50) . "...");
                            } else {
                                $this->add_result($test_name, false, "Field $field missing or empty");
                            }
                        }
                        
                    } else {
                        $this->add_result($test_name, false, "JSON is empty or not an array");
                    }
                    
                } else {
                    $this->add_result($test_name, false, "JSON is invalid: " . json_last_error_msg());
                }
                
            } else {
                $this->add_result($test_name, false, "Example JSON file not found: $json_file");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test WordPress functions availability
     */
    private function test_wordpress_functions() {
        $test_name = "WordPress Functions Test";
        
        try {
            $required_functions = [
                'wp_insert_post',
                'wp_upload_dir',
                'sanitize_text_field',
                'sanitize_textarea_field',
                'update_post_meta',
                'set_post_thumbnail'
            ];
            
            foreach ($required_functions as $function) {
                if (function_exists($function)) {
                    $this->add_result($test_name, true, "Function $function is available");
                } else {
                    $this->add_result($test_name, false, "Function $function is not available");
                }
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Add test result
     */
    private function add_result($test_name, $success, $message) {
        if (!isset($this->test_results[$test_name])) {
            $this->test_results[$test_name] = [];
        }
        
        $this->test_results[$test_name][] = [
            'success' => $success,
            'message' => $message
        ];
    }
    
    /**
     * Display test results
     */
    private function display_results() {
        echo "<h2>Test Results</h2>\n";
        
        foreach ($this->test_results as $test_name => $results) {
            echo "<h3>$test_name</h3>\n";
            echo "<ul>\n";
            
            foreach ($results as $result) {
                $status = $result['success'] ? '✅' : '❌';
                $color = $result['success'] ? 'green' : 'red';
                echo "<li style='color: $color'>$status " . htmlspecialchars($result['message']) . "</li>\n";
            }
            
            echo "</ul>\n";
        }
        
        // Summary
        $total_tests = 0;
        $passed_tests = 0;
        
        foreach ($this->test_results as $results) {
            foreach ($results as $result) {
                $total_tests++;
                if ($result['success']) {
                    $passed_tests++;
                }
            }
        }
        
        echo "<h2>Summary</h2>\n";
        echo "<p><strong>Passed: $passed_tests / $total_tests tests</strong></p>\n";
        
        if ($passed_tests === $total_tests) {
            echo "<p style='color: green; font-weight: bold;'>🎉 All tests passed! Yoast SEO integration is working correctly.</p>\n";
        } else {
            echo "<p style='color: red; font-weight: bold;'>⚠️ Some tests failed. Please review the issues above.</p>\n";
        }
    }
}

// Run tests if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'test-yoast-integration.php') {
    new Rife_PG_Yoast_Integration_Test();
}
?>
