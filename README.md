# Rife PageGenerator

A powerful WordPress plugin for creating beautiful landing pages with ease. Generate professional landing pages using pre-built templates, customize styles, and manage content through an intuitive interface.

## Features

- **Template System**: Choose from multiple professionally designed templates
- **Visual Style Customizer**: Customize colors, fonts, spacing, and more with live preview
- **Content Management**: Easy-to-use forms for adding content to different page sections
- **Dummy Content Generator**: ✅ **WORKING** - One-click fill with sample content for testing
- **Yoast SEO Integration**: ✅ **NEW** - Automatic SEO optimization for bulk generated pages
- **Responsive Design**: All templates are mobile-friendly and responsive
- **Page Management**: View, edit, duplicate, and delete generated pages
- **AJAX-Powered**: Smooth user experience with real-time updates
- **Database Integration**: Robust data storage and management

## Installation

1. Download the plugin files
2. Upload to your WordPress `/wp-content/plugins/` directory
3. Activate the plugin through the 'Plugins' menu in WordPress
4. Navigate to 'Rife PageGenerator' in your WordPress admin menu

## Quick Start

1. **Browse Templates**: Go to Rife PageGenerator → Template Gallery to view available templates
2. **Create a Page**: Go to Rife PageGenerator → Page Generator and select a template
3. **Add Content**: Fill in the content forms for each section (or use "Fill Dummy Content" for testing)
4. **Customize Style**: Use the design settings to match your brand colors and typography
5. **Generate**: Click "Generate Page" to create your WordPress page

## Templates

### SEO Comprehensive ✅ **AVAILABLE**
- Comprehensive landing page with all essential sections
- Sections: Hero, Benefits, Testimonials, Services/Pricing, Process, Detailed Pricing, Guarantees, Guides, Locations, About, Contact, Final CTA
- Perfect for: SEO agencies, digital marketing services, comprehensive business solutions
- Features: Indonesian dummy content, extensive customization options

### Additional Templates (Coming Soon)
- Business Basic: Clean professional design
- Tech Startup: Modern gradient design with animations
- E-commerce Product: Conversion-focused sales pages

## Style Customization

The style customizer allows you to:

- **Colors**: Primary, secondary, accent, text, and background colors
- **Typography**: Choose from Google Fonts for headings and body text
- **Layout**: Adjust container width, section padding, border radius
- **Buttons**: Select button styles (square, rounded, pill)
- **Shadows**: Choose shadow intensity (none, subtle, medium, strong)

### Style Presets

- **Modern Blue**: Contemporary design with blue color scheme
- **Classic Professional**: Traditional and professional styling
- **Vibrant Creative**: Bold and creative design approach

## Content Sections

### Hero Section
- Main title and subtitle
- Call-to-action button with link
- Background customization

### Benefits/Features
- Multiple benefit cards with icons
- Title and description for each benefit
- Customizable icons using Font Awesome

### Pricing
- Multiple pricing plans
- Feature lists for each plan
- Call-to-action buttons

### Testimonials
- Customer testimonials with photos
- Name and company information
- Social proof elements

### Contact
- Contact information display
- Email, phone, and address fields
- Integration-ready for contact forms

## Yoast SEO Integration

Rife PageGenerator now includes automatic Yoast SEO integration for bulk generated pages:

### Supported SEO Fields
- **Focus Keyphrase**: Target keyword for each page (`yoast_focus_keyphrase`)
- **SEO Title**: Custom title for search engines (`yoast_seo_title`)
- **Meta Description**: Search result description (`yoast_meta_description`)

### JSON Example with SEO
```json
{
  "hero_title": "Your Page Title",
  "hero_subtitle": "Your page subtitle",
  "yoast_focus_keyphrase": "your target keyword",
  "yoast_seo_title": "Custom SEO Title | Your Brand",
  "yoast_meta_description": "Compelling meta description with your keyword included."
}
```

### Bulk Generation with SEO
When using the Bulk Generate feature with JSON files containing Yoast SEO fields:
1. Include the SEO fields in your JSON structure
2. Upload the JSON file through the Bulk Generate interface
3. Pages will be generated with SEO metadata automatically populated
4. No manual SEO configuration needed for each page

See [YOAST_SEO_INTEGRATION.md](YOAST_SEO_INTEGRATION.md) for detailed documentation.

## Database Structure

The plugin creates the following database tables:

- `wp_rife_pg_pages`: Stores generated page data
- `wp_rife_pg_templates`: Template definitions and metadata
- `wp_rife_pg_styles`: Saved style presets and user styles
- `wp_rife_pg_analytics`: Usage analytics and tracking

## Hooks and Filters

### Actions

```php
// Before page generation
do_action('rife_pg_before_page_generation', $template_id, $content_data, $style_data);

// After page generation
do_action('rife_pg_after_page_generation', $page_id, $template_id, $content_data);

// Before template rendering
do_action('rife_pg_before_template_render', $template_id, $content_data);
```

### Filters

```php
// Modify template data
$template = apply_filters('rife_pg_template_data', $template, $template_id);

// Modify generated content
$content = apply_filters('rife_pg_generated_content', $content, $template_id, $content_data);

// Modify style CSS
$css = apply_filters('rife_pg_style_css', $css, $style_data);
```

## API Reference

### Template Manager

```php
// Get all templates
$templates = rife_pg()->template_manager->get_available_templates();

// Get specific template
$template = rife_pg()->template_manager->get_template($template_id);

// Validate template data
$is_valid = rife_pg()->template_manager->validate_template_data($template_id, $content_data);
```

### Page Generator

```php
// Generate a page
$result = rife_pg()->page_generator->generate_page($template_id, $content_data, $style_data);

// Delete a page
$result = rife_pg()->page_generator->delete_page($page_id);
```

### Style Customizer

```php
// Get available fonts
$fonts = rife_pg()->style_customizer->get_available_fonts();

// Generate CSS from style data
$css = rife_pg()->style_customizer->generate_css($style_data);
```

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Changelog

### Version 1.0.2 (Current)
- ✅ **ADDED**: Yoast SEO integration for bulk generated pages
- ✅ **ADDED**: Support for yoast_focus_keyphrase, yoast_seo_title, and yoast_meta_description in JSON files
- ✅ **ADDED**: Automatic SEO metadata population for bulk generated pages
- ✅ **ADDED**: Example JSON file with Yoast SEO fields (example-with-yoast.json)
- ✅ **ADDED**: Comprehensive documentation for Yoast SEO integration
- ✅ **IMPROVED**: Enhanced bulk generate process with SEO optimization

### Version 1.0.1
- ✅ **FIXED**: "Fill Dummy Content" functionality now working properly
- ✅ **FIXED**: Design settings (colors, typography, spacing) now properly applied to generated pages
- ✅ **IMPROVED**: Enhanced error handling and debugging
- ✅ **ADDED**: Comprehensive Indonesian dummy content for SEO template
- ✅ **UPDATED**: AJAX nonce handling for better security

### Version 1.0.0
- Initial release
- Template system with SEO Comprehensive template
- Style customizer with live preview
- Page management interface
- Database integration
- AJAX handlers

## Support

For support and questions:
- Documentation: [Plugin Documentation](https://example.com/docs)
- Support Forum: [WordPress.org Support](https://wordpress.org/support/plugin/rife-pagegenerator)
- GitHub Issues: [Report Issues](https://github.com/example/rife-pagegenerator/issues)

## License

This plugin is licensed under the GPL v2 or later.

## Credits

- Bootstrap CSS Framework
- Font Awesome Icons
- Google Fonts
- WordPress Plugin Boilerplate

---

**Rife PageGenerator** - Create beautiful landing pages with ease.
