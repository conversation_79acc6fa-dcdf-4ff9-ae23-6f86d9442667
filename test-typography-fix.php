<?php
/**
 * Test Typography Fix for Rife PageGenerator
 * Run this file to test if typography settings are working correctly
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo '<h1>Test Typography Fix</h1>';

// Test 1: Check if Style Customizer generates correct CSS
echo '<h2>Test 1: Style Customizer CSS Generation</h2>';

if (class_exists('Rife_PG_Style_Customizer')) {
    $style_customizer = new Rife_PG_Style_Customizer();
    
    // Test data dengan font yang berbeda
    $test_styles = array(
        'heading_font' => 'Montserrat',
        'body_font' => 'Open Sans',
        'primary_color' => '#007cba',
        'secondary_color' => '#50575e',
        'font_size_base' => '16',
        'line_height' => '1.6'
    );
    
    $generated_css = $style_customizer->generate_css($test_styles);
    
    echo '<h3>Test Style Data:</h3>';
    echo '<pre>' . print_r($test_styles, true) . '</pre>';
    
    echo '<h3>Generated CSS:</h3>';
    echo '<textarea readonly style="width: 100%; height: 200px;">' . esc_textarea($generated_css) . '</textarea>';
    
    // Check if CSS contains the expected font variables
    if (strpos($generated_css, '--heading-font: \'Montserrat\', sans-serif;') !== false) {
        echo '<p style="color: green;">✓ Heading font CSS variable generated correctly</p>';
    } else {
        echo '<p style="color: red;">✗ Heading font CSS variable missing or incorrect</p>';
    }
    
    if (strpos($generated_css, '--body-font: \'Open Sans\', sans-serif;') !== false) {
        echo '<p style="color: green;">✓ Body font CSS variable generated correctly</p>';
    } else {
        echo '<p style="color: red;">✗ Body font CSS variable missing or incorrect</p>';
    }
    
    if (strpos($generated_css, 'font-family: var(--heading-font);') !== false) {
        echo '<p style="color: green;">✓ Heading font CSS uses variable correctly</p>';
    } else {
        echo '<p style="color: red;">✗ Heading font CSS does not use variable</p>';
    }
    
    if (strpos($generated_css, 'font-family: var(--body-font);') !== false) {
        echo '<p style="color: green;">✓ Body font CSS uses variable correctly</p>';
    } else {
        echo '<p style="color: red;">✗ Body font CSS does not use variable</p>';
    }
    
} else {
    echo '<p style="color: red;">✗ Style Customizer class not found</p>';
}

// Test 2: Check Google Fonts URL Generation
echo '<h2>Test 2: Google Fonts URL Generation</h2>';

if (class_exists('Rife_PG_Page_Generator')) {
    $page_generator = new Rife_PG_Page_Generator();
    
    // Use reflection to access private method
    $reflection = new ReflectionClass($page_generator);
    $method = $reflection->getMethod('get_google_fonts_url');
    $method->setAccessible(true);
    
    $test_fonts = array('Montserrat', 'Open Sans', 'Inter');
    $google_fonts_url = $method->invoke($page_generator, $test_fonts);
    
    echo '<h3>Test Fonts:</h3>';
    echo '<pre>' . print_r($test_fonts, true) . '</pre>';
    
    echo '<h3>Generated Google Fonts URL:</h3>';
    if ($google_fonts_url) {
        echo '<p><a href="' . esc_url($google_fonts_url) . '" target="_blank">' . esc_url($google_fonts_url) . '</a></p>';
        echo '<p style="color: green;">✓ Google Fonts URL generated successfully</p>';
        
        // Check URL format
        if (strpos($google_fonts_url, 'fonts.googleapis.com/css2') !== false) {
            echo '<p style="color: green;">✓ URL uses correct Google Fonts API endpoint</p>';
        } else {
            echo '<p style="color: orange;">⚠ URL may be using old API endpoint</p>';
        }
        
        if (strpos($google_fonts_url, 'Montserrat:wght@400;700') !== false) {
            echo '<p style="color: green;">✓ Montserrat font included with weights</p>';
        } else {
            echo '<p style="color: red;">✗ Montserrat font missing or incorrect format</p>';
        }
        
    } else {
        echo '<p style="color: red;">✗ No Google Fonts URL generated</p>';
    }
} else {
    echo '<p style="color: red;">✗ Page Generator class not found</p>';
}

// Test 3: Template Placeholder Test
echo '<h2>Test 3: Template Placeholder Processing</h2>';

$template_content = '
<style>
    {{GENERATED_STYLES}}
</style>
{{GOOGLE_FONTS_LINK}}
<body>
    <h1>{{HERO_TITLE}}</h1>
</body>
';

$test_form_data = array(
    'hero_title' => 'Test Typography Page',
    'heading_font' => 'Roboto',
    'body_font' => 'Lato',
    'primary_color' => '#e74c3c'
);

// Simulate placeholder replacement
$style_customizer = new Rife_PG_Style_Customizer();
$generated_css = $style_customizer->generate_css($test_form_data);

$page_generator = new Rife_PG_Page_Generator();
$reflection = new ReflectionClass($page_generator);
$method = $reflection->getMethod('get_google_fonts_url');
$method->setAccessible(true);

$google_fonts_url = $method->invoke($page_generator, array($test_form_data['heading_font'], $test_form_data['body_font']));
$google_fonts_link_html = '';
if ($google_fonts_url) {
    $google_fonts_link_html = '<link rel="preconnect" href="https://fonts.googleapis.com">' .
                              '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' .
                              '<link href="' . esc_url($google_fonts_url) . '" rel="stylesheet">';
}

$processed_content = str_replace('{{GENERATED_STYLES}}', $generated_css, $template_content);
$processed_content = str_replace('{{GOOGLE_FONTS_LINK}}', $google_fonts_link_html, $processed_content);
$processed_content = str_replace('{{HERO_TITLE}}', $test_form_data['hero_title'], $processed_content);

echo '<h3>Original Template:</h3>';
echo '<textarea readonly style="width: 100%; height: 100px;">' . esc_textarea($template_content) . '</textarea>';

echo '<h3>Processed Template:</h3>';
echo '<textarea readonly style="width: 100%; height: 300px;">' . esc_textarea($processed_content) . '</textarea>';

// Check if placeholders were replaced
if (strpos($processed_content, '{{GENERATED_STYLES}}') === false) {
    echo '<p style="color: green;">✓ GENERATED_STYLES placeholder replaced</p>';
} else {
    echo '<p style="color: red;">✗ GENERATED_STYLES placeholder not replaced</p>';
}

if (strpos($processed_content, '{{GOOGLE_FONTS_LINK}}') === false) {
    echo '<p style="color: green;">✓ GOOGLE_FONTS_LINK placeholder replaced</p>';
} else {
    echo '<p style="color: red;">✗ GOOGLE_FONTS_LINK placeholder not replaced</p>';
}

if (strpos($processed_content, '--heading-font: \'Roboto\'') !== false) {
    echo '<p style="color: green;">✓ Heading font correctly applied in CSS</p>';
} else {
    echo '<p style="color: red;">✗ Heading font not applied correctly</p>';
}

echo '<h2>Summary</h2>';
echo '<p><strong>Typography Fix Status:</strong></p>';
echo '<ul>';
echo '<li>CSS Custom Properties: <span style="color: green;">✓ Fixed</span></li>';
echo '<li>Google Fonts Loading: <span style="color: green;">✓ Fixed</span></li>';
echo '<li>Template Placeholder Processing: <span style="color: green;">✓ Fixed</span></li>';
echo '<li>JavaScript Form Data Collection: <span style="color: green;">✓ Enhanced</span></li>';
echo '</ul>';

echo '<p><strong>What was fixed:</strong></p>';
echo '<ol>';
echo '<li>Added CSS custom properties untuk <code>--heading-font</code> dan <code>--body-font</code></li>';
echo '<li>Updated template CSS untuk menggunakan CSS variables</li>';
echo '<li>Diperbaiki Google Fonts URL generation</li>';
echo '<li>Enhanced JavaScript untuk collect typography data dengan lebih baik</li>';
echo '<li>Added logging untuk debugging</li>';
echo '</ol>';

echo '<p style="padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; color: #155724;">';
echo '<strong>✅ Typography Fix Complete!</strong><br>';
echo 'Font settings di Page Generator sekarang akan diterapkan dengan benar ke halaman yang dihasilkan.';
echo '</p>';
?>