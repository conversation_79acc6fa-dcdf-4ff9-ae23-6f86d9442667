<?php
/**
 * Fix validation issues for Rife PageGenerator
 * Run this file to fix common validation problems
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

echo '<h1>Rife PageGenerator Validation Fix</h1>';

// Fix 1: Ensure database tables exist
echo '<h2>1. Checking Database Tables</h2>';

if (function_exists('rife_pg') && isset(rife_pg()->database)) {
    rife_pg()->database->create_tables();
    echo '<p style="color: green;">✓ Database tables created/updated</p>';
} else {
    // Fallback table creation
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    
    $table_name = $wpdb->prefix . 'rife_pg_pages';
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        page_id bigint(20) NOT NULL,
        template_id varchar(100) NOT NULL,
        style_data longtext,
        content_data longtext,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY page_id (page_id),
        KEY template_id (template_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    echo '<p style="color: green;">✓ Basic table created</p>';
}

// Fix 2: Test template validation
echo '<h2>2. Testing Template Validation</h2>';

if (function_exists('rife_pg') && isset(rife_pg()->template_manager)) {
    $template_manager = rife_pg()->template_manager;
    
    // Test with minimal valid data
    $test_data = array(
        'hero' => array(
            'title' => 'Test Title',
            'subtitle' => 'Test Subtitle',
            'cta_text' => 'Get Started',
            'cta_link' => '#'
        )
    );
    
    $validation_result = $template_manager->validate_template_data('business-basic', $test_data);
    
    if ($validation_result === true) {
        echo '<p style="color: green;">✓ Template validation working correctly</p>';
    } else {
        echo '<p style="color: red;">✗ Template validation failed:</p>';
        if (is_array($validation_result)) {
            foreach ($validation_result as $error) {
                echo '<p style="color: red;">- ' . $error . '</p>';
            }
        }
    }
} else {
    echo '<p style="color: red;">✗ Template manager not available</p>';
}

// Fix 3: Test AJAX handler
echo '<h2>3. Testing AJAX Handler</h2>';

if (class_exists('Rife_PG_Ajax_Handler')) {
    echo '<p style="color: green;">✓ AJAX Handler class exists</p>';
    
    // Check if actions are registered
    $actions = array(
        'wp_ajax_rife_pg_generate_page',
        'wp_ajax_rife_pg_preview_template',
        'wp_ajax_rife_pg_delete_page'
    );
    
    foreach ($actions as $action) {
        if (has_action($action)) {
            echo '<p style="color: green;">✓ ' . $action . ' registered</p>';
        } else {
            echo '<p style="color: red;">✗ ' . $action . ' not registered</p>';
        }
    }
} else {
    echo '<p style="color: red;">✗ AJAX Handler class not found</p>';
}

// Fix 4: Create test page to verify generation works
echo '<h2>4. Testing Page Generation</h2>';

if (function_exists('rife_pg') && isset(rife_pg()->page_generator)) {
    $page_generator = rife_pg()->page_generator;
    
    $test_content = array(
        'hero' => array(
            'title' => 'Test Landing Page',
            'subtitle' => 'This is a test page generated by the fix script',
            'cta_text' => 'Learn More',
            'cta_link' => '#'
        )
    );
    
    $test_styles = array(
        'primary_color' => '#007cba',
        'secondary_color' => '#50575e'
    );
    
    try {
        $result = $page_generator->generate_page('business-basic', $test_content, $test_styles);
        
        if ($result['success']) {
            echo '<p style="color: green;">✓ Test page generated successfully</p>';
            echo '<p>Page ID: ' . $result['page_id'] . '</p>';
            echo '<p>Page URL: <a href="' . $result['page_url'] . '" target="_blank">' . $result['page_url'] . '</a></p>';
        } else {
            echo '<p style="color: red;">✗ Page generation failed: ' . $result['message'] . '</p>';
        }
    } catch (Exception $e) {
        echo '<p style="color: red;">✗ Exception during page generation: ' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p style="color: red;">✗ Page generator not available</p>';
}

// Fix 5: Clear any cached data that might be causing issues
echo '<h2>5. Clearing Cache</h2>';

delete_transient('rife_pg_templates_cache');
delete_transient('rife_pg_styles_cache');

if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
}

echo '<p style="color: green;">✓ Cache cleared</p>';

// Fix 6: Reset plugin options if needed
echo '<h2>6. Plugin Options</h2>';

$options = get_option('rife_pg_options', array());
if (empty($options)) {
    $default_options = array(
        'auto_publish' => false,
        'delete_data_on_uninstall' => false,
        'enable_analytics' => true
    );
    update_option('rife_pg_options', $default_options);
    echo '<p style="color: green;">✓ Default options set</p>';
} else {
    echo '<p style="color: green;">✓ Plugin options exist</p>';
}

// Fix 7: JavaScript debugging helper
echo '<h2>7. JavaScript Debug Helper</h2>';
echo '<p>Open browser console and run the following to test form data collection:</p>';
echo '<textarea readonly style="width: 100%; height: 150px;">
// Test form data collection
var testData = {
    template_id: "business-basic",
    content_data: {
        hero: {
            title: "Test Title",
            subtitle: "Test Subtitle",
            cta_text: "Get Started",
            cta_link: "#"
        }
    },
    style_data: {
        primary_color: "#007cba",
        secondary_color: "#50575e"
    },
    auto_publish: false
};

console.log("Test data:", testData);

// Test AJAX call
jQuery.ajax({
    url: ajaxurl,
    type: "POST",
    data: {
        action: "rife_pg_generate_page",
        template_id: testData.template_id,
        content_data: testData.content_data,
        style_data: testData.style_data,
        auto_publish: testData.auto_publish,
        nonce: "' . wp_create_nonce('rife_pg_nonce') . '"
    },
    success: function(response) {
        console.log("Success:", response);
    },
    error: function(xhr, status, error) {
        console.log("Error:", xhr.responseText);
    }
});
</textarea>';

// Summary
echo '<h2>Summary</h2>';
echo '<p>If you are still experiencing the "Template validation failed" error, please:</p>';
echo '<ol>';
echo '<li>Check the browser console for JavaScript errors</li>';
echo '<li>Ensure all form fields have proper names (content[hero][title], etc.)</li>';
echo '<li>Verify that the hero title field is not empty</li>';
echo '<li>Check the WordPress error log for PHP errors</li>';
echo '<li>Try deactivating and reactivating the plugin</li>';
echo '</ol>';

echo '<p><strong>Common fixes:</strong></p>';
echo '<ul>';
echo '<li>Make sure you have filled in at least the Hero Title field</li>';
echo '<li>Check that JavaScript is enabled in your browser</li>';
echo '<li>Verify that AJAX requests are not being blocked</li>';
echo '<li>Ensure proper WordPress permissions</li>';
echo '</ul>';
?>
