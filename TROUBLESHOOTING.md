# Troubleshooting Guide - Rife PageGenerator Yoast SEO Integration

## Common Issues and Solutions

### 1. Yoast SEO Data Not Appearing

**Symptoms:**
- Generated pages don't have Yoast SEO metadata
- Focus keyphrase, SEO title, or meta description are empty
- Yoast SEO analysis shows no data

**Solutions:**

1. **Check Yoast SEO Plugin Status**
   ```bash
   # Verify Yoast SEO is installed and activated
   # Go to WordPress Admin > Plugins
   # Ensure "Yoast SEO" is active
   ```

2. **Verify JSON Structure**
   - Ensure your JSON file includes the required Yoast fields:
     - `yoast_focus_keyphrase`
     - `yoast_seo_title`
     - `yoast_meta_description`

3. **Check Error Logs**
   ```bash
   # Check WordPress error logs
   tail -f /path/to/wordpress/wp-content/debug.log
   
   # Look for "Rife PG:" entries
   grep "Rife PG:" /path/to/wordpress/wp-content/debug.log
   ```

### 2. Image Generation Issues

**Symptoms:**
- No featured images on generated pages
- Error messages about GD library
- Fallback images not working

**Solutions:**

1. **Check GD Library**
   ```php
   <?php
   // Add this to a test file to check GD status
   if (extension_loaded('gd')) {
       echo "GD Library is available\n";
       print_r(gd_info());
   } else {
       echo "GD Library is NOT available\n";
   }
   ?>
   ```

2. **Check File Permissions**
   ```bash
   # Ensure upload directory is writable
   chmod 755 /path/to/wordpress/wp-content/uploads/
   chown www-data:www-data /path/to/wordpress/wp-content/uploads/
   ```

3. **Verify Focus Keyphrase**
   - Ensure each JSON item has a `yoast_focus_keyphrase` field
   - The keyphrase is used for image generation

### 3. Bulk Generation Timeouts

**Symptoms:**
- Process stops after generating some pages
- "Maximum execution time exceeded" errors
- Incomplete bulk generation

**Solutions:**

1. **Increase PHP Limits**
   ```php
   // Add to wp-config.php or .htaccess
   ini_set('max_execution_time', 300); // 5 minutes
   ini_set('memory_limit', '256M');
   ```

2. **Process Smaller Batches**
   - Split large JSON files into smaller chunks
   - Process 10-20 pages at a time instead of hundreds

3. **Check Server Resources**
   ```bash
   # Monitor server resources during bulk generation
   top -p $(pgrep php)
   ```

### 4. JSON Validation Errors

**Symptoms:**
- "Invalid JSON structure" error messages
- Bulk generation fails to start
- Parsing errors in logs

**Solutions:**

1. **Validate JSON Syntax**
   ```bash
   # Use online JSON validators or command line
   cat your-file.json | python -m json.tool
   ```

2. **Check Required Fields**
   - Each item must have at least `hero_title`
   - Yoast fields are optional but recommended

3. **Encoding Issues**
   - Ensure file is saved as UTF-8
   - Check for special characters

### 5. Permission Issues

**Symptoms:**
- "You do not have permission" errors
- 403 Forbidden responses
- Cannot access bulk generation feature

**Solutions:**

1. **Check User Capabilities**
   ```php
   // User must have 'manage_options' capability
   if (current_user_can('manage_options')) {
       echo "User has required permissions";
   }
   ```

2. **Verify Nonce**
   - Clear browser cache
   - Refresh the page and try again

## Debugging Steps

### 1. Enable Debug Logging

Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### 2. Run Integration Test

```bash
# Navigate to plugin directory
cd /path/to/wp-content/plugins/Rife-PageGenerator/

# Run the integration test
php run-integration-test.php
```

### 3. Check Plugin Status

```php
// Add this to a test file
if (class_exists('Rife_PageGenerator')) {
    echo "Plugin loaded successfully\n";
} else {
    echo "Plugin not loaded\n";
}

if (class_exists('Rife_PG_Image_Generator')) {
    echo "Image generator available\n";
} else {
    echo "Image generator not available\n";
}
```

### 4. Test Individual Components

```php
// Test Yoast SEO detection
if (class_exists('WPSEO_Options') || defined('WPSEO_VERSION')) {
    echo "Yoast SEO detected\n";
} else {
    echo "Yoast SEO not found\n";
}

// Test GD Library
if (extension_loaded('gd')) {
    echo "GD Library available\n";
} else {
    echo "GD Library not available\n";
}
```

## Performance Optimization

### 1. Server Requirements

**Minimum:**
- PHP 7.4+
- Memory: 128MB
- Execution time: 60 seconds

**Recommended:**
- PHP 8.0+
- Memory: 256MB
- Execution time: 300 seconds

### 2. Batch Processing

For large datasets:
1. Split JSON into files of 10-20 items each
2. Process one file at a time
3. Monitor server resources between batches

### 3. Image Optimization

1. **Use appropriate image sizes**
   - Default: 1200x630 (optimal for social sharing)
   - Adjust in `modules/class-image-generator.php` if needed

2. **Font optimization**
   - Plugin tries to use system fonts first
   - Falls back to GD built-in fonts if needed

## Error Codes Reference

| Code | Message | Solution |
|------|---------|----------|
| 403 | Security check failed | Refresh page, check nonce |
| 403 | Permission denied | Ensure user has admin rights |
| 400 | No file uploaded | Check file upload in form |
| 400 | Invalid JSON structure | Validate JSON syntax |
| 500 | Internal server error | Check error logs |

## Getting Help

1. **Check Documentation**
   - `README.md` - General plugin information
   - `YOAST_SEO_INTEGRATION.md` - Integration specifics
   - `TEMPLATE_DEVELOPMENT.md` - Template customization

2. **Run Diagnostics**
   ```bash
   php run-integration-test.php > diagnostic-report.txt
   ```

3. **Collect Information**
   - WordPress version
   - PHP version
   - Yoast SEO version
   - Error logs
   - JSON file sample

4. **Contact Support**
   - Include diagnostic report
   - Provide specific error messages
   - Describe steps to reproduce issue

## Prevention Tips

1. **Regular Testing**
   - Test with small JSON files first
   - Verify on staging environment

2. **Backup Strategy**
   - Backup database before bulk operations
   - Keep original JSON files

3. **Monitoring**
   - Monitor error logs during bulk generation
   - Check server resources

4. **Updates**
   - Keep WordPress updated
   - Keep Yoast SEO updated
   - Update plugin when available
