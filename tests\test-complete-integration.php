<?php
/**
 * Complete Integration Test
 * 
 * This test simulates the complete workflow of bulk generation with Yoast SEO
 * to ensure all components work together correctly.
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h1>Rife PageGenerator - Complete Integration Test</h1>\n";

// Include required files
require_once dirname(__FILE__) . '/../modules/class-image-generator.php';

class Rife_PG_Complete_Integration_Test {
    
    private $test_results = [];
    private $generated_files = [];
    
    public function __construct() {
        $this->run_all_tests();
        $this->display_results();
        $this->cleanup();
    }
    
    /**
     * Run all integration tests
     */
    private function run_all_tests() {
        $this->test_file_structure();
        $this->test_json_data();
        $this->test_yoast_seo_extraction();
        $this->test_image_generation_workflow();
        $this->test_error_scenarios();
    }
    
    /**
     * Test file structure
     */
    private function test_file_structure() {
        $test_name = "File Structure Test";
        
        $required_files = [
            '../rife-pagegenerator.php' => 'Main plugin file',
            '../includes/class-page-generator.php' => 'Page Generator class',
            '../includes/class-ajax-handler.php' => 'AJAX Handler class',
            '../modules/class-image-generator.php' => 'Image Generator module',
            '../admin/views/example-with-yoast.json' => 'Example JSON with Yoast data'
        ];
        
        foreach ($required_files as $file => $description) {
            $full_path = dirname(__FILE__) . '/' . $file;
            if (file_exists($full_path)) {
                $this->add_result($test_name, true, "$description exists");
            } else {
                $this->add_result($test_name, false, "$description missing: $file");
            }
        }
    }
    
    /**
     * Test JSON data structure
     */
    private function test_json_data() {
        $test_name = "JSON Data Test";
        
        try {
            $json_file = dirname(__FILE__) . '/../admin/views/example-with-yoast.json';
            
            if (!file_exists($json_file)) {
                $this->add_result($test_name, false, "JSON file not found");
                return;
            }
            
            $json_content = file_get_contents($json_file);
            $data = json_decode($json_content, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->add_result($test_name, false, "JSON parsing error: " . json_last_error_msg());
                return;
            }
            
            $this->add_result($test_name, true, "JSON parsed successfully");
            $this->add_result($test_name, true, "Contains " . count($data) . " sample pages");
            
            // Test first item structure
            if (!empty($data[0])) {
                $first_item = $data[0];
                
                // Check required fields
                $required_fields = ['hero_title'];
                foreach ($required_fields as $field) {
                    if (isset($first_item[$field]) && !empty($first_item[$field])) {
                        $this->add_result($test_name, true, "Required field '$field' present");
                    } else {
                        $this->add_result($test_name, false, "Required field '$field' missing");
                    }
                }
                
                // Check Yoast SEO fields
                $yoast_fields = [
                    'yoast_focus_keyphrase' => 'Focus Keyphrase',
                    'yoast_seo_title' => 'SEO Title',
                    'yoast_meta_description' => 'Meta Description'
                ];
                
                foreach ($yoast_fields as $field => $label) {
                    if (isset($first_item[$field]) && !empty($first_item[$field])) {
                        $value = substr($first_item[$field], 0, 50) . '...';
                        $this->add_result($test_name, true, "$label present: $value");
                    } else {
                        $this->add_result($test_name, false, "$label missing or empty");
                    }
                }
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test Yoast SEO data extraction simulation
     */
    private function test_yoast_seo_extraction() {
        $test_name = "Yoast SEO Extraction Test";
        
        try {
            // Simulate form data from JSON
            $test_data = [
                'hero_title' => 'Test SEO Page',
                'yoast_focus_keyphrase' => 'test seo keyword',
                'yoast_seo_title' => 'Test SEO Page | Professional Services',
                'yoast_meta_description' => 'This is a test meta description for SEO optimization with the focus keyword included.'
            ];
            
            // Simulate the extraction process
            $yoast_data = $this->simulate_yoast_extraction($test_data);
            
            if (!empty($yoast_data)) {
                $this->add_result($test_name, true, "Yoast SEO data extracted successfully");
                
                // Check specific fields
                $expected_fields = [
                    '_yoast_wpseo_focuskw' => $test_data['yoast_focus_keyphrase'],
                    '_yoast_wpseo_title' => $test_data['yoast_seo_title'],
                    '_yoast_wpseo_metadesc' => $test_data['yoast_meta_description']
                ];
                
                foreach ($expected_fields as $field => $expected_value) {
                    if (isset($yoast_data[$field]) && $yoast_data[$field] === $expected_value) {
                        $this->add_result($test_name, true, "Field $field correctly extracted");
                    } else {
                        $this->add_result($test_name, false, "Field $field extraction failed");
                    }
                }
                
                // Check additional fields
                $additional_fields = [
                    '_yoast_wpseo_meta-robots-noindex',
                    '_yoast_wpseo_meta-robots-nofollow',
                    '_yoast_wpseo_content_score'
                ];
                
                foreach ($additional_fields as $field) {
                    if (isset($yoast_data[$field])) {
                        $this->add_result($test_name, true, "Additional field $field set: " . $yoast_data[$field]);
                    } else {
                        $this->add_result($test_name, false, "Additional field $field missing");
                    }
                }
                
            } else {
                $this->add_result($test_name, false, "No Yoast SEO data extracted");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test complete image generation workflow
     */
    private function test_image_generation_workflow() {
        $test_name = "Image Generation Workflow Test";
        
        try {
            if (!class_exists('Rife_PG_Image_Generator')) {
                $this->add_result($test_name, false, "Image generator class not available");
                return;
            }
            
            // Simulate the complete workflow
            $test_data = [
                'hero_title' => 'Professional SEO Services Jakarta',
                'yoast_focus_keyphrase' => 'seo services jakarta'
            ];
            
            $image_generator = new Rife_PG_Image_Generator();
            
            // Step 1: Generate hero image
            $hero_image = $image_generator->generate_image(
                $test_data['yoast_focus_keyphrase'] . ' Excellence',
                $test_data['yoast_focus_keyphrase']
            );
            
            if ($hero_image['success']) {
                $this->add_result($test_name, true, "Hero image generated successfully");
                $this->add_result($test_name, true, "Image URL: " . $hero_image['url']);
                $this->add_result($test_name, true, "ALT text: " . $hero_image['alt_text']);
                
                $this->generated_files[] = $hero_image['path'];
                
                // Verify ALT text contains keyword
                if (strpos(strtolower($hero_image['alt_text']), strtolower($test_data['yoast_focus_keyphrase'])) !== false) {
                    $this->add_result($test_name, true, "ALT text contains focus keyphrase");
                } else {
                    $this->add_result($test_name, false, "ALT text does not contain focus keyphrase");
                }
                
            } else {
                $this->add_result($test_name, false, "Hero image generation failed: " . ($hero_image['error'] ?? 'Unknown error'));
            }
            
            // Step 2: Test image set generation
            $image_set = $image_generator->generate_image_set($test_data['yoast_focus_keyphrase'], 'benefits');
            
            if (!empty($image_set)) {
                $this->add_result($test_name, true, "Image set generated: " . count($image_set) . " images");
                
                foreach ($image_set as $image) {
                    if ($image['success']) {
                        $this->generated_files[] = $image['path'];
                    }
                }
            } else {
                $this->add_result($test_name, false, "Image set generation failed");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Test error scenarios
     */
    private function test_error_scenarios() {
        $test_name = "Error Scenarios Test";
        
        try {
            if (!class_exists('Rife_PG_Image_Generator')) {
                $this->add_result($test_name, false, "Image generator class not available");
                return;
            }
            
            $image_generator = new Rife_PG_Image_Generator();
            
            // Test 1: Empty keyphrase
            $test_data_empty = [];
            $yoast_data = $this->simulate_yoast_extraction($test_data_empty);
            
            if (empty($yoast_data)) {
                $this->add_result($test_name, true, "Correctly handled empty Yoast data");
            } else {
                $this->add_result($test_name, false, "Should have returned empty array for no Yoast data");
            }
            
            // Test 2: Invalid image generation
            $result = $image_generator->generate_image('', '');
            if (!$result['success']) {
                $this->add_result($test_name, true, "Correctly handled invalid image generation");
            } else {
                $this->add_result($test_name, false, "Should have failed with empty parameters");
            }
            
            // Test 3: Very long values
            $long_data = [
                'yoast_focus_keyphrase' => str_repeat('very long keyword ', 10),
                'yoast_seo_title' => str_repeat('Very long title ', 10),
                'yoast_meta_description' => str_repeat('Very long description ', 20)
            ];
            
            $yoast_data = $this->simulate_yoast_extraction($long_data);
            if (!empty($yoast_data)) {
                $this->add_result($test_name, true, "Handled long values correctly");
            } else {
                $this->add_result($test_name, false, "Failed to handle long values");
            }
            
        } catch (Exception $e) {
            $this->add_result($test_name, false, "Exception: " . $e->getMessage());
        }
    }
    
    /**
     * Simulate Yoast SEO data extraction
     */
    private function simulate_yoast_extraction($form_data) {
        $yoast_data = array();
        
        // Extract focus keyphrase
        if (!empty($form_data['yoast_focus_keyphrase'])) {
            $focus_kw = trim($form_data['yoast_focus_keyphrase']);
            $yoast_data['_yoast_wpseo_focuskw'] = $focus_kw;
            $yoast_data['_yoast_wpseo_focuskw_text_input'] = $focus_kw;
        }
        
        // Extract SEO title
        if (!empty($form_data['yoast_seo_title'])) {
            $seo_title = trim($form_data['yoast_seo_title']);
            $yoast_data['_yoast_wpseo_title'] = $seo_title;
        }
        
        // Extract meta description
        if (!empty($form_data['yoast_meta_description'])) {
            $meta_desc = trim($form_data['yoast_meta_description']);
            $yoast_data['_yoast_wpseo_metadesc'] = $meta_desc;
        }
        
        // Set additional defaults if we have any Yoast data
        if (!empty($yoast_data)) {
            $yoast_data['_yoast_wpseo_meta-robots-noindex'] = '0';
            $yoast_data['_yoast_wpseo_meta-robots-nofollow'] = '0';
            $yoast_data['_yoast_wpseo_content_score'] = '30';
            $yoast_data['_yoast_wpseo_linkdex'] = '30';
        }
        
        return $yoast_data;
    }
    
    /**
     * Add test result
     */
    private function add_result($test_name, $success, $message) {
        if (!isset($this->test_results[$test_name])) {
            $this->test_results[$test_name] = [];
        }
        
        $this->test_results[$test_name][] = [
            'success' => $success,
            'message' => $message
        ];
    }
    
    /**
     * Display test results
     */
    private function display_results() {
        echo "<h2>Complete Integration Test Results</h2>\n";
        
        foreach ($this->test_results as $test_name => $results) {
            echo "<h3>$test_name</h3>\n";
            echo "<ul>\n";
            
            foreach ($results as $result) {
                $status = $result['success'] ? '✅' : '❌';
                $color = $result['success'] ? 'green' : 'red';
                echo "<li style='color: $color'>$status " . htmlspecialchars($result['message']) . "</li>\n";
            }
            
            echo "</ul>\n";
        }
        
        // Summary
        $total_tests = 0;
        $passed_tests = 0;
        
        foreach ($this->test_results as $results) {
            foreach ($results as $result) {
                $total_tests++;
                if ($result['success']) {
                    $passed_tests++;
                }
            }
        }
        
        echo "<h2>Final Summary</h2>\n";
        echo "<p><strong>Passed: $passed_tests / $total_tests tests</strong></p>\n";
        
        if ($passed_tests === $total_tests) {
            echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 ALL INTEGRATION TESTS PASSED!</p>\n";
            echo "<p style='color: green;'>The Yoast SEO integration is working correctly and ready for production use.</p>\n";
        } else {
            $failed_tests = $total_tests - $passed_tests;
            echo "<p style='color: red; font-weight: bold; font-size: 18px;'>⚠️ $failed_tests tests failed</p>\n";
            echo "<p style='color: red;'>Please review the failed tests above before using in production.</p>\n";
        }
        
        echo "<p><strong>Generated " . count($this->generated_files) . " test images during testing</strong></p>\n";
    }
    
    /**
     * Cleanup generated test files
     */
    private function cleanup() {
        echo "<h2>Cleanup</h2>\n";
        
        $cleaned = 0;
        foreach ($this->generated_files as $file) {
            if (file_exists($file)) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }
        
        if ($cleaned > 0) {
            echo "<p style='color: green;'>✅ Cleaned up $cleaned test files</p>\n";
        }
        
        echo "<p><small>Test completed at " . date('Y-m-d H:i:s') . "</small></p>\n";
    }
}

// Run tests if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'test-complete-integration.php') {
    new Rife_PG_Complete_Integration_Test();
}
?>
