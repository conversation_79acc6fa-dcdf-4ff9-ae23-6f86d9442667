<?php
/**
 * Plugin Name: Rife PageGenerator
 * Plugin URI: https://www.nusawebsite.com
 * Description: A powerful landing page generator plugin for WordPress. Create stunning landing pages with customizable templates, styling options, and content management.
 * Version: 1.0.0
 * Author: <PERSON><PERSON>
 * Author URI: https://www.nusawebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: rife-pagegenerator
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('RIFE_PG_VERSION', '1.0.0');
define('RIFE_PG_PLUGIN_FILE', __FILE__);
define('RIFE_PG_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('RIFE_PG_PLUGIN_URL', plugin_dir_url(__FILE__));
define('RIFE_PG_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class Rife_PageGenerator {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Plugin version
     */
    public $version = RIFE_PG_VERSION;
    
    /**
     * Database instance
     */
    public $database = null;
    
    /**
     * Admin instance
     */
    public $admin = null;
    
    /**
     * Template manager instance
     */
    public $template_manager = null;
    
    /**
     * Page generator instance
     */
    public $page_generator = null;
    
    /**
     * AJAX handler instance
     */
    public $ajax_handler = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->includes();
        $this->init();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        register_activation_hook(RIFE_PG_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(RIFE_PG_PLUGIN_FILE, array($this, 'deactivate'));
        
        add_action('init', array($this, 'load_textdomain'));
        add_action('plugins_loaded', array($this, 'plugins_loaded'));
    }
    
    /**
     * Include required files
     */
    private function includes() {
        // Core classes
        require_once RIFE_PG_PLUGIN_DIR . 'includes/class-database.php';
        require_once RIFE_PG_PLUGIN_DIR . 'includes/class-admin.php';
        require_once RIFE_PG_PLUGIN_DIR . 'includes/class-template-manager.php';
        require_once RIFE_PG_PLUGIN_DIR . 'includes/class-page-generator.php';
        require_once RIFE_PG_PLUGIN_DIR . 'includes/class-style-customizer.php';
        require_once RIFE_PG_PLUGIN_DIR . 'includes/class-ajax-handler.php';
        require_once RIFE_PG_PLUGIN_DIR . 'includes/class-form-builder.php';

        // Modules
        require_once RIFE_PG_PLUGIN_DIR . 'modules/class-image-generator.php';
    }
    
    /**
     * Initialize plugin components
     */
    private function init() {
        // Initialize database
        $this->database = new Rife_PG_Database();

        // Initialize admin interface
        if (is_admin()) {
            $this->admin = new Rife_PG_Admin();
        }

        // Initialize template manager
        $this->template_manager = new Rife_PG_Template_Manager();

        // Initialize page generator
        $this->page_generator = new Rife_PG_Page_Generator();

        // Initialize AJAX handler
        $this->ajax_handler = new Rife_PG_Ajax_Handler();
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables if needed
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Load text domain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'rife-pagegenerator',
            false,
            dirname(RIFE_PG_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Plugins loaded hook
     */
    public function plugins_loaded() {
        // Additional initialization after all plugins are loaded
        do_action('rife_pg_loaded');
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        // Use the database class to create tables
        if ($this->database) {
            $this->database->create_tables();
        } else {
            // Fallback to basic table creation
            global $wpdb;

            $charset_collate = $wpdb->get_charset_collate();

            // Table for storing generated pages metadata
            $table_name = $wpdb->prefix . 'rife_pg_pages';

            $sql = "CREATE TABLE $table_name (
                id mediumint(9) NOT NULL AUTO_INCREMENT,
                page_id bigint(20) NOT NULL,
                template_id varchar(100) NOT NULL,
                style_data longtext,
                content_data longtext,
                created_at datetime DEFAULT CURRENT_TIMESTAMP,
                updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY page_id (page_id),
                KEY template_id (template_id)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);

            // Store database version
            add_option('rife_pg_db_version', '1.0');
        }
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $default_options = array(
            'version' => RIFE_PG_VERSION,
            'default_template' => 'business-basic',
            'enable_preview' => true,
            'auto_publish' => false,
            'default_colors' => array(
                'primary' => '#007cba',
                'secondary' => '#50575e'
            ),
            'default_fonts' => array(
                'heading' => 'Inter',
                'body' => 'Inter'
            )
        );
        
        add_option('rife_pg_options', $default_options);
    }
    
    /**
     * Get plugin option
     */
    public function get_option($key, $default = null) {
        $options = get_option('rife_pg_options', array());
        return isset($options[$key]) ? $options[$key] : $default;
    }
    
    /**
     * Update plugin option
     */
    public function update_option($key, $value) {
        $options = get_option('rife_pg_options', array());
        $options[$key] = $value;
        update_option('rife_pg_options', $options);
    }
}

/**
 * Get main plugin instance
 */
function rife_pg() {
    return Rife_PageGenerator::instance();
}

// Initialize the plugin
rife_pg();
