<?php
/**
 * Style Customizer for Rife PageGenerator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rife_PG_Style_Customizer {
    
    /**
     * Default style options
     */
    private $default_styles = array();
    
    /**
     * Available fonts
     */
    private $available_fonts = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_default_styles();
        $this->init_available_fonts();
    }
    
    /**
     * Initialize default style options
     */
    private function init_default_styles() {
        $this->default_styles = array(
            'primary_color' => '#007cba',
            'secondary_color' => '#50575e',
            'accent_color' => '#00a32a',
            'text_color' => '#1e1e1e',
            'background_color' => '#ffffff',
            'heading_font' => 'Inter',
            'body_font' => 'Inter',
            'font_size_base' => '16',
            'line_height' => '1.6',
            'section_padding' => '80',
            'element_spacing' => '24',
            'container_width' => '1200',
            'border_radius' => '8',
            'button_style' => 'rounded',
            'shadow_style' => 'subtle'
        );
    }
    
    /**
     * Initialize available fonts
     */
    private function init_available_fonts() {
        $this->available_fonts = array(
            'Inter' => 'Inter',
            'Roboto' => 'Roboto',
            'Open Sans' => 'Open Sans',
            'Lato' => 'Lato',
            'Montserrat' => 'Montserrat',
            'Poppins' => 'Poppins',
            'Source Sans Pro' => 'Source Sans Pro',
            'Nunito' => 'Nunito',
            'Raleway' => 'Raleway',
            'Ubuntu' => 'Ubuntu',
            'Playfair Display' => 'Playfair Display',
            'Merriweather' => 'Merriweather',
            'Georgia' => 'Georgia',
            'Times New Roman' => 'Times New Roman',
            'Arial' => 'Arial',
            'Helvetica' => 'Helvetica'
        );
    }
    
    /**
     * Get default styles
     */
    public function get_default_styles() {
        return $this->default_styles;
    }
    
    /**
     * Get available fonts
     */
    public function get_available_fonts() {
        return $this->available_fonts;
    }
    
    /**
     * Render style customizer panel
     */
    public function render_customizer_panel($current_styles = array()) {
        $styles = wp_parse_args($current_styles, $this->default_styles);
        
        ob_start();
        ?>
        <div class="rife-pg-style-customizer">
            <div class="customizer-header">
                <h3><?php echo 'Style Customizer'; ?></h3>
                <p><?php echo 'Customize the appearance of your landing page'; ?></p>
            </div>
            
            <div class="customizer-sections">
                <!-- Colors Section -->
                <div class="customizer-section" data-section="colors">
                    <h4 class="section-title">
                        <span class="dashicons dashicons-admin-appearance"></span>
                        <?php echo 'Colors'; ?>
                    </h4>
                    <div class="section-content">
                        <div class="color-control">
                            <label for="primary_color"><?php echo 'Primary Color'; ?></label>
                            <input type="text" id="primary_color" name="primary_color" 
                                   value="<?php echo esc_attr($styles['primary_color']); ?>" 
                                   class="color-picker" data-default-color="<?php echo esc_attr($this->default_styles['primary_color']); ?>">
                        </div>
                        
                        <div class="color-control">
                            <label for="secondary_color"><?php echo 'Secondary Color'; ?></label>
                            <input type="text" id="secondary_color" name="secondary_color" 
                                   value="<?php echo esc_attr($styles['secondary_color']); ?>" 
                                   class="color-picker" data-default-color="<?php echo esc_attr($this->default_styles['secondary_color']); ?>">
                        </div>
                        
                        <div class="color-control">
                            <label for="accent_color"><?php echo 'Accent Color'; ?></label>
                            <input type="text" id="accent_color" name="accent_color" 
                                   value="<?php echo esc_attr($styles['accent_color']); ?>" 
                                   class="color-picker" data-default-color="<?php echo esc_attr($this->default_styles['accent_color']); ?>">
                        </div>
                        
                        <div class="color-control">
                            <label for="text_color"><?php echo 'Text Color'; ?></label>
                            <input type="text" id="text_color" name="text_color" 
                                   value="<?php echo esc_attr($styles['text_color']); ?>" 
                                   class="color-picker" data-default-color="<?php echo esc_attr($this->default_styles['text_color']); ?>">
                        </div>
                        
                        <div class="color-control">
                            <label for="background_color"><?php echo 'Background Color'; ?></label>
                            <input type="text" id="background_color" name="background_color" 
                                   value="<?php echo esc_attr($styles['background_color']); ?>" 
                                   class="color-picker" data-default-color="<?php echo esc_attr($this->default_styles['background_color']); ?>">
                        </div>
                    </div>
                </div>
                
                <!-- Typography Section -->
                <div class="customizer-section" data-section="typography">
                    <h4 class="section-title">
                        <span class="dashicons dashicons-editor-textcolor"></span>
                        <?php echo 'Typography'; ?>
                    </h4>
                    <div class="section-content">
                        <div class="font-control">
                            <label for="heading_font"><?php echo 'Heading Font'; ?></label>
                            <select id="heading_font" name="heading_font">
                                <?php foreach ($this->available_fonts as $value => $label): ?>
                                    <option value="<?php echo esc_attr($value); ?>" 
                                            <?php selected($styles['heading_font'], $value); ?>>
                                        <?php echo esc_html($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="font-control">
                            <label for="body_font"><?php echo 'Body Font'; ?></label>
                            <select id="body_font" name="body_font">
                                <?php foreach ($this->available_fonts as $value => $label): ?>
                                    <option value="<?php echo esc_attr($value); ?>" 
                                            <?php selected($styles['body_font'], $value); ?>>
                                        <?php echo esc_html($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="range-control">
                            <label for="font_size_base"><?php echo 'Base Font Size'; ?></label>
                            <div class="range-wrapper">
                                <input type="range" id="font_size_base" name="font_size_base" 
                                       min="12" max="24" step="1" 
                                       value="<?php echo esc_attr($styles['font_size_base']); ?>">
                                <span class="range-value"><?php echo esc_html($styles['font_size_base']); ?>px</span>
                            </div>
                        </div>
                        
                        <div class="range-control">
                            <label for="line_height"><?php echo 'Line Height'; ?></label>
                            <div class="range-wrapper">
                                <input type="range" id="line_height" name="line_height" 
                                       min="1.2" max="2.0" step="0.1" 
                                       value="<?php echo esc_attr($styles['line_height']); ?>">
                                <span class="range-value"><?php echo esc_html($styles['line_height']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Spacing Section -->
                <div class="customizer-section" data-section="spacing">
                    <h4 class="section-title">
                        <span class="dashicons dashicons-editor-expand"></span>
                        <?php echo 'Spacing & Layout'; ?>
                    </h4>
                    <div class="section-content">
                        <div class="range-control">
                            <label for="section_padding"><?php echo 'Section Padding'; ?></label>
                            <div class="range-wrapper">
                                <input type="range" id="section_padding" name="section_padding" 
                                       min="40" max="120" step="10" 
                                       value="<?php echo esc_attr($styles['section_padding']); ?>">
                                <span class="range-value"><?php echo esc_html($styles['section_padding']); ?>px</span>
                            </div>
                        </div>
                        
                        <div class="range-control">
                            <label for="container_width"><?php echo 'Container Width'; ?></label>
                            <div class="range-wrapper">
                                <input type="range" id="container_width" name="container_width" 
                                       min="960" max="1400" step="40" 
                                       value="<?php echo esc_attr($styles['container_width']); ?>">
                                <span class="range-value"><?php echo esc_html($styles['container_width']); ?>px</span>
                            </div>
                        </div>
                        
                        <div class="range-control">
                            <label for="border_radius"><?php echo 'Border Radius'; ?></label>
                            <div class="range-wrapper">
                                <input type="range" id="border_radius" name="border_radius" 
                                       min="0" max="20" step="2" 
                                       value="<?php echo esc_attr($styles['border_radius']); ?>">
                                <span class="range-value"><?php echo esc_html($styles['border_radius']); ?>px</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Button & Effects Section -->
                <div class="customizer-section" data-section="effects">
                    <h4 class="section-title">
                        <span class="dashicons dashicons-admin-customizer"></span>
                        <?php echo 'Buttons & Effects'; ?>
                    </h4>
                    <div class="section-content">
                        <div class="select-control">
                            <label for="button_style"><?php echo 'Button Style'; ?></label>
                            <select id="button_style" name="button_style">
                                <option value="rounded" <?php selected($styles['button_style'], 'rounded'); ?>>
                                    <?php echo 'Rounded'; ?>
                                </option>
                                <option value="square" <?php selected($styles['button_style'], 'square'); ?>>
                                    <?php echo 'Square'; ?>
                                </option>
                                <option value="pill" <?php selected($styles['button_style'], 'pill'); ?>>
                                    <?php echo 'Pill'; ?>
                                </option>
                            </select>
                        </div>
                        
                        <div class="select-control">
                            <label for="shadow_style"><?php echo 'Shadow Style'; ?></label>
                            <select id="shadow_style" name="shadow_style">
                                <option value="none" <?php selected($styles['shadow_style'], 'none'); ?>>
                                    <?php echo 'None'; ?>
                                </option>
                                <option value="subtle" <?php selected($styles['shadow_style'], 'subtle'); ?>>
                                    <?php echo 'Subtle'; ?>
                                </option>
                                <option value="medium" <?php selected($styles['shadow_style'], 'medium'); ?>>
                                    <?php echo 'Medium'; ?>
                                </option>
                                <option value="strong" <?php selected($styles['shadow_style'], 'strong'); ?>>
                                    <?php echo 'Strong'; ?>
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="customizer-actions">
                <button type="button" class="button button-secondary" id="reset-styles">
                    <?php echo 'Reset to Default'; ?>
                </button>
                <button type="button" class="button button-primary" id="preview-styles">
                    <?php echo 'Preview Changes'; ?>
                </button>
            </div>
        </div>
        <?php
        
        return ob_get_clean();
    }
    
    /**
     * Generate CSS from style data
     */
    public function generate_css($style_data) {
        $styles = wp_parse_args($style_data, $this->default_styles);
        
        $css = ":root {\n";
        $css .= "  --primary-color: {$styles['primary_color']};\n";
        $css .= "  --secondary-color: {$styles['secondary_color']};\n";
        $css .= "  --accent-color: {$styles['accent_color']};\n";
        $css .= "  --text-color: {$styles['text_color']};\n";
        $css .= "  --background-color: {$styles['background_color']};\n";
        $css .= "  --font-size-base: {$styles['font_size_base']}px;\n";
        $css .= "  --line-height: {$styles['line_height']};\n";
        // Handle section padding - might be "80px 0" or just "80"
        $section_padding = $styles['section_padding'];
        if (is_string($section_padding) && strpos($section_padding, 'px') !== false) {
            // Extract numeric value from "80px 0" format
            preg_match('/(\d+)px/', $section_padding, $matches);
            $section_padding_value = isset($matches[1]) ? $matches[1] : '80';
        } else {
            $section_padding_value = $section_padding;
        }
        $css .= "  --section-padding: {$section_padding_value}px;\n";
        
        // Handle element spacing - new CSS custom property
        $element_spacing = isset($styles['element_spacing']) ? $styles['element_spacing'] : '24px';
        if (is_string($element_spacing) && strpos($element_spacing, 'px') !== false) {
            // Extract numeric value from "24px" format
            preg_match('/(\d+)px/', $element_spacing, $matches);
            $element_spacing_value = isset($matches[1]) ? $matches[1] : '24';
        } else {
            $element_spacing_value = $element_spacing;
        }
        $css .= "  --element-spacing: {$element_spacing_value}px;\n";
        
        $css .= "  --container-width: {$styles['container_width']}px;\n";
        
        // Handle border radius - might already have px unit
        $border_radius = $styles['border_radius'];
        if (is_string($border_radius) && strpos($border_radius, 'px') !== false) {
            $css .= "  --border-radius: {$border_radius};\n";
        } else {
            $css .= "  --border-radius: {$border_radius}px;\n";
        }
        $css .= "  --heading-font: '{$styles['heading_font']}', sans-serif;\n";
        $css .= "  --body-font: '{$styles['body_font']}', sans-serif;\n";
        $css .= "}\n\n";
        
        // Typography
        $css .= "body, p, div, span {\n";
        $css .= "  font-family: var(--body-font);\n";
        $css .= "  font-size: var(--font-size-base);\n";
        $css .= "  line-height: var(--line-height);\n";
        $css .= "  color: var(--text-color);\n";
        $css .= "}\n\n";
        
        $css .= "h1, h2, h3, h4, h5, h6 {\n";
        $css .= "  font-family: var(--heading-font);\n";
        $css .= "}\n\n";
        
        // Layout
        $css .= ".container {\n";
        $css .= "  max-width: var(--container-width);\n";
        $css .= "}\n\n";
        
        $css .= ".section {\n";
        $css .= "  padding: var(--section-padding) 0;\n";
        $css .= "}\n\n";
        
        // Buttons
        $button_radius = $this->get_button_radius($styles['button_style'], $styles['border_radius']);
        $css .= ".btn, .button {\n";
        $css .= "  border-radius: {$button_radius};\n";
        $css .= "  background-color: var(--primary-color);\n";
        $css .= "}\n\n";
        
        // Shadows
        if ($styles['shadow_style'] !== 'none') {
            $shadow = $this->get_shadow_style($styles['shadow_style']);
            $css .= ".card, .btn, .form-control {\n";
            $css .= "  box-shadow: {$shadow};\n";
            $css .= "}\n\n";
        }
        
        return $css;
    }
    
    /**
     * Get button radius based on style
     */
    private function get_button_radius($button_style, $base_radius) {
        switch ($button_style) {
            case 'square':
                return '0px';
            case 'pill':
                return '50px';
            case 'rounded':
            default:
                return $base_radius . 'px';
        }
    }
    
    /**
     * Get shadow style
     */
    private function get_shadow_style($shadow_style) {
        switch ($shadow_style) {
            case 'subtle':
                return '0 1px 3px rgba(0,0,0,0.1)';
            case 'medium':
                return '0 4px 6px rgba(0,0,0,0.1)';
            case 'strong':
                return '0 10px 25px rgba(0,0,0,0.15)';
            default:
                return 'none';
        }
    }
    
    /**
     * Validate style data
     */
    public function validate_style_data($style_data) {
        $errors = array();
        
        // Validate colors
        $color_fields = array('primary_color', 'secondary_color', 'accent_color', 'text_color', 'background_color');
        foreach ($color_fields as $field) {
            if (isset($style_data[$field]) && !$this->is_valid_color($style_data[$field])) {
                $errors[] = sprintf('Invalid color value for %s', $field);
            }
        }
        
        // Validate numeric ranges - with px unit handling
        $numeric_fields = array(
            'font_size_base' => array('min' => 12, 'max' => 24),
            'line_height' => array('min' => 1.2, 'max' => 2.0),
            'section_padding' => array('min' => 40, 'max' => 120),
            'element_spacing' => array('min' => 16, 'max' => 40),
            'container_width' => array('min' => 960, 'max' => 1400),
            'border_radius' => array('min' => 0, 'max' => 20)
        );
        
        foreach ($numeric_fields as $field => $range) {
            if (isset($style_data[$field])) {
                // Handle values with px unit
                $value = $style_data[$field];
                if (is_string($value) && strpos($value, 'px') !== false) {
                    $value = intval(str_replace('px', '', $value));
                } else {
                    $value = floatval($value);
                }
                
                if ($value < $range['min'] || $value > $range['max']) {
                    $errors[] = sprintf(
                        '%s must be between %s and %s',
                        $field,
                        $range['min'],
                        $range['max']
                    );
                }
            }
        }
        
        return empty($errors) ? true : $errors;
    }
    
    /**
     * Check if color value is valid
     */
    private function is_valid_color($color) {
        return preg_match('/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $color);
    }
}
