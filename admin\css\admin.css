/**
 * Admin Styles for Rife PageGenerator
 */

/* ========================================
   BASE & GLOBAL STYLES
   ======================================== */

body {
    background: #f8fafc; /* Lighter background for the whole page */
}

.rife-pg-admin {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    padding: 0 32px; /* More padding for full-width */
}

.rife-pg-admin .description {
    font-size: 15px;
    color: #64748b;
    margin-bottom: 24px;
}

/* ========================================
   MODERN FULL-WIDTH LAYOUT
   ======================================== */

/* Modern Header */
.rife-pg-header {
    background: transparent; /* Cleaner header */
    padding: 32px 0;
    margin-bottom: 24px;
    border-bottom: 1px solid #e2e8f0;
}

.rife-pg-header .wp-heading-inline {
    color: #1e293b;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 8px;
}

.rife-pg-header .description {
    color: #64748b;
    font-size: 16px;
    margin: 0;
    max-width: 800px; /* Limit description width for readability */
}

/* Progress Steps */
.progress-steps {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-top: 24px;
    padding: 16px;
    background: #fff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

.progress-step {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #94a3b8;
    flex: 1;
    position: relative;
    padding: 8px;
    transition: all 0.3s ease;
}

.progress-step:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 28px;
    background-color: #e2e8f0;
}

.progress-step.active {
    color: #4f46e5;
    font-weight: 600;
}

.progress-step.completed {
    color: #334155;
}

.progress-step-number {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    font-weight: 600;
    background: #f1f5f9;
    color: #64748b;
    transition: all 0.3s ease;
}

.progress-step.active .progress-step-number {
    background: #4f46e5;
    color: white;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
}

.progress-step.completed .progress-step-number {
    background: #818cf8;
    color: white;
}

/* Main layout - 80:20 split */
.rife-pg-layout {
    display: grid;
    grid-template-columns: minmax(0, 1fr) 340px; /* Fluid main content, fixed sidebar */
    gap: 32px;
    align-items: start;
    align-items: start;
    width: 100%;
}

/* Area kiri (Form content) */
.rife-pg-main-content {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

.content-header {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e2e8f0;
}

.content-header h2 {
    font-size: 22px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 4px 0;
}

.content-header p {
    font-size: 15px;
    color: #64748b;
    margin: 0;
}

.content-sections-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
}

.content-sections-container.masonry-layout {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    grid-auto-rows: auto;
    transition: all 0.3s ease;
}

.content-sections-container.masonry-layout .content-section {
    break-inside: avoid;
    page-break-inside: avoid;
    margin-bottom: 0;
    transition: all 0.3s ease;
    height: auto;
    align-self: start;
}

/* Responsive adjustments for masonry layout */
@media (max-width: 1200px) {
    .content-sections-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .content-sections-container.masonry-layout {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .content-sections-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .content-sections-container.masonry-layout {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}

.content-section {
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    background: #f8fafc;
    transition: all 0.2s ease;
    break-inside: avoid;
    margin-bottom: 24px;
    display: grid;
    grid-template-rows: auto 1fr;
    align-items: start;
}

/* Untuk masonry layout, margin-bottom diatur oleh gap grid */
.content-sections-container.masonry-layout .content-section {
    margin-bottom: 0;
}

.content-section:hover {
    border-color: #a5b4fc;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

.content-section .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #334155;
    margin: 0 0 24px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.content-section .section-title .dashicons {
    color: #6366f1;
    font-size: 22px;
}

.section-description {
    font-size: 14px;
    color: #64748b;
    margin: -16px 0 16px 0;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.seo-tip {
    font-size: 12px;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 6px;
    background: #f1f5f9;
    padding: 8px 12px;
    border-radius: 6px;
    margin-top: 8px;
}

.seo-tip .dashicons {
    font-size: 16px;
}

/* Area kanan (Sidebar) */
.rife-pg-sidebar {
    display: flex;
    flex-direction: column;
    gap: 24px;
    position: sticky;
    top: 40px;
}

.sidebar-card {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
}

.sidebar-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.04);
}

.sidebar-card .card-header {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
}

.sidebar-card .card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #334155;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sidebar-card .card-header .dashicons {
    color: #6366f1;
}

.sidebar-card .card-content {
    padding: 20px;
}

/* Highlight for Template Card */
.sidebar-card.template-card {
    border-color: #6366f1;
    background: #f8fafc;
}

/* ========================================
   MODERN FORM ELEMENTS
   ======================================== */

.field-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.field-group label {
    font-weight: 500;
    color: #334155;
    font-size: 14px;
}

.field-group label .required {
    color: #ef4444;
    font-weight: 600;
    margin-left: 4px;
}

.field-group input,
.field-group textarea,
.field-group select {
    padding: 10px 14px;
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    font-size: 14px;
    background: #fff;
    transition: all 0.2s ease;
}

.field-group input:focus,
.field-group textarea:focus,
.field-group select:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    outline: none;
}

.field-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* Design Controls in Sidebar */
.design-controls {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.design-controls h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #475569;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 12px;
}

.control-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 16px;
}

.control-grid .control-group {
    gap: 6px;
}

.control-grid .control-group label {
    font-size: 12px;
}

.control-grid input[type="color"] {
    width: 100%;
    height: 32px;
    padding: 0;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
}

.control-group-text {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 20px;
    border: 1px solid transparent;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #4f46e5;
    color: white;
}
.btn-primary:hover {
    background: #4338ca;
    color: white;
}

.btn-secondary {
    background: #fff;
    color: #4f46e5;
    border-color: #e2e8f0;
}
.btn-secondary:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.btn-hero {
    padding: 12px 24px;
    font-size: 15px;
}

/* Generation Options */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #334155;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #4f46e5;
}

.generation-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

/* ========================================
   TEMPLATE GALLERY STYLES
   ======================================== */

.rife-pg-gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
}

.gallery-filters .filter-btn {
    background: transparent;
    border: 1px solid #e2e8f0;
    color: #64748b;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
}

.gallery-filters .filter-btn.active {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;
}

.gallery-search input {
    padding: 8px 12px;
    border: 1px solid #cbd5e1;
    border-radius: 8px;
    font-size: 14px;
    width: 250px;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
}

.template-card {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

.template-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.07), 0 4px 6px -2px rgba(0, 0, 0, 0.04);
}

.template-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.template-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.template-card:hover .template-overlay {
    opacity: 1;
}

.template-info {
    padding: 20px;
}

.template-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.template-category {
    background: #eef2ff;
    color: #4f46e5;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.template-description {
    color: #64748b;
    font-size: 14px;
    margin: 12px 0;
}

.sections-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
}

.section-tag {
    background: #f1f5f9;
    color: #64748b;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
}

/* ========================================
   UTILITIES & RESPONSIVE
   ======================================== */

/* Hide WordPress Footer */
#wpfooter {
    display: none !important;
}

/* Responsive */
@media (max-width: 1200px) {
    .rife-pg-layout {
        grid-template-columns: minmax(0, 1fr) 300px; /* Adjust for smaller screens */
        gap: 24px;
    }
}

@media (max-width: 960px) {
    .rife-pg-layout {
        grid-template-columns: 1fr;
    }

    .rife-pg-sidebar {
        position: static;
        order: -1; /* Move sidebar to top on mobile */
    }

    /* Content sections 2 columns on tablet */
    .content-sections-container .content-section {
        width: calc(50% - 16px);
    }
}

@media (max-width: 768px) {
    .rife-pg-admin {
        padding: 0 20px;
    }

    .field-row {
        grid-template-columns: 1fr;
    }

    /* Content sections back to single column on mobile */
    .content-sections-container .content-section {
        width: 100%;
        margin: 8px 0;
    }
}

/* ========================================
   TEMPLATE GALLERY BUTTONS
   ======================================== */

.btn-outline {
    background: transparent;
    color: #3b82f6;
    border: 1px solid #3b82f6;
}

.btn-outline:hover {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.template-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.template-actions .btn {
    flex: 1;
    min-width: 80px;
    font-size: 12px;
    padding: 8px 12px;
}

/* Modal Styles */
.rife-pg-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 1200px;
    height: 80%;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
/* Masonry layout optimizations */
.content-sections-container.masonry-layout {
    align-items: start;
}

.content-sections-container.masonry-layout .content-section {
    align-self: start;
    margin-bottom: 0;
    break-inside: avoid;
    page-break-inside: avoid;
}

/* Ensure smooth transitions for masonry items */
.content-section {
    transition: all 0.3s ease;
}

/* Hover effect for masonry items */
.content-sections-container.masonry-layout .content-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Base responsive layout untuk container biasa */
@media (min-width: 1201px) {
    .content-sections-container {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1200px) and (min-width: 769px) {
    .content-sections-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .content-sections-container {
        grid-template-columns: 1fr;
    }
}

/* Responsive adjustments untuk masonry layout */
@media (max-width: 1200px) {
    .content-sections-container.masonry-layout {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .content-sections-container.masonry-layout {
        grid-template-columns: 1fr;
        gap: 16px;
    }
}
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
}

.modal-close:hover {
    background: #f1f5f9;
}

.modal-body {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.preview-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

#template-preview-frame {
    width: 100%;
    height: 100%;
    border: none;
    display: none;
}