<?php
/**
 * Static Form Builder for Rife PageGenerator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Rife_PG_Form_Builder {

    // --- Content Form Rendering ---

    public function render_form($config) {
        if (empty($config['sections']) || !is_array($config['sections'])) {
            return;
        }
        foreach ($config['sections'] as $section_id => $section) {
            $this->render_section($section_id, $section);
        }
    }

    private function render_section($id, $section) {
        ?>
        <div class="content-section" data-section="<?php echo esc_attr($id); ?>">
            <h3 class="section-title">
                <span class="dashicons dashicons-edit"></span>
                <?php echo esc_html($section['name']); ?>
            </h3>
            <div class="section-fields">
                <?php if (!empty($section['description'])) : ?>
                    <p class="section-description"><?php echo esc_html($section['description']); ?></p>
                <?php endif; ?>

                <?php
                if (!empty($section['fields']) && is_array($section['fields'])) {
                    foreach ($section['fields'] as $field_id => $field) {
                        $this->render_field(sprintf('content[%s][%s]', $id, $field_id), $field);
                    }
                }
                ?>
            </div>
        </div>
        <?php
    }

    // --- Style Form Rendering ---

    public function render_style_form($config) {
        if (empty($config['style_options']) || !is_array($config['style_options'])) {
            return;
        }

        foreach ($config['style_options'] as $group_id => $group) {
            $this->render_style_group($group_id, $group);
        }
    }

    private function render_style_group($id, $group) {
        ?>
        <h4><?php echo esc_html(ucfirst($id)); ?></h4>
        <?php
        foreach ($group as $field_id => $field) {
            $this->render_field(sprintf('styles[%s]', $field_id), $field);
        }
        echo '<hr>';
    }

    // --- Generic Field Rendering ---

    private function render_field($name, $field) {
        $field_type = $field['type'] ?? 'text';
        ?>
        <div class="field-group field-type-<?php echo esc_attr($field_type); ?>">
            <label for="<?php echo esc_attr($name); ?>">
                <?php echo esc_html($field['label']); ?>
                <?php if (!empty($field['required'])) : ?>
                    <span class="required">*</span>
                <?php endif; ?>
            </label>

            <?php
            switch ($field_type) {
                case 'textarea':
                    $this->render_textarea($name, $field);
                    break;
                case 'checkbox':
                    $this->render_checkbox($name, $field);
                    break;
                case 'select':
                    $this->render_select($name, $field);
                    break;
                default:
                    $this->render_text_input($name, $field);
                    break;
            }
            ?>
        </div>
        <?php
    }

    private function render_text_input($name, $field) {
        $type = $field['type'] ?? 'text';
        ?>
        <input 
            type="<?php echo esc_attr($type); ?>" 
            id="<?php echo esc_attr($name); ?>" 
            name="<?php echo esc_attr($name); ?>" 
            placeholder="<?php echo esc_attr($field['placeholder'] ?? ''); ?>"
            value="<?php echo esc_attr($field['default'] ?? ''); ?>"
            <?php echo !empty($field['required']) ? 'required' : ''; ?>
        >
        <?php
    }

    private function render_textarea($name, $field) {
        ?>
        <textarea 
            id="<?php echo esc_attr($name); ?>" 
            name="<?php echo esc_attr($name); ?>" 
            rows="4" 
            placeholder="<?php echo esc_attr($field['placeholder'] ?? ''); ?>"
            <?php echo !empty($field['required']) ? 'required' : ''; ?>
        ><?php echo esc_textarea($field['default'] ?? ''); ?></textarea>
        <?php
    }

    private function render_checkbox($name, $field) {
        ?>
        <label class="checkbox-label">
            <input 
                type="checkbox" 
                id="<?php echo esc_attr($name); ?>" 
                name="<?php echo esc_attr($name); ?>" 
                value="1"
                <?php checked(true, !empty($field['default'])); ?>
            >
            <span><?php echo esc_html($field['description'] ?? ''); ?></span>
        </label>
        <?php
    }

    private function render_select($name, $field) {
        ?>
        <select id="<?php echo esc_attr($name); ?>" name="<?php echo esc_attr($name); ?>">
            <?php foreach ($field['options'] as $value => $label): ?>
                <option value="<?php echo esc_attr($value); ?>" <?php selected($field['default'] ?? '', $value); ?>>
                    <?php echo esc_html($label); ?>
                </option>
            <?php endforeach; ?>
        </select>
        <?php
    }
}