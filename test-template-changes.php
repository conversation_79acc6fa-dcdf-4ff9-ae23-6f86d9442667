<?php
/**
 * Test Template Changes for SEO Comprehensive
 */

// Load WordPress environment
require_once dirname(__FILE__) . '/../../../wp-load.php';

echo '<h1>Test Template Changes - SEO Comprehensive</h1>';
echo '<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }</style>';

// Test 1: Check if defaults.php has been updated
echo '<h2>Test 1: Defaults.php Content Check</h2>';
$defaults_file = dirname(__FILE__) . '/templates/seo-comprehensive/defaults.php';

if (file_exists($defaults_file)) {
    echo '<p class="success">✓ defaults.php file exists</p>';
    
    $defaults_content = include $defaults_file;
    
    // Check hero title
    if (isset($defaults_content['content']['hero']['title']) && 
        strpos($defaults_content['content']['hero']['title'], 'Jasa Pembuatan Website') !== false) {
        echo '<p class="success">✓ Hero title updated correctly</p>';
    } else {
        echo '<p class="error">✗ Hero title not updated</p>';
    }
    
    // Check pricing
    if (isset($defaults_content['content']['services']['plan_1_title']) && 
        $defaults_content['content']['services']['plan_1_title'] === 'BASIC') {
        echo '<p class="success">✓ Pricing plans updated correctly</p>';
    } else {
        echo '<p class="error">✗ Pricing plans not updated</p>';
    }
    
    // Check testimonials
    if (isset($defaults_content['content']['testimonials']['testimonial_1_name']) && 
        strpos($defaults_content['content']['testimonials']['testimonial_1_name'], 'Toko Online Fashion') !== false) {
        echo '<p class="success">✓ Testimonials updated correctly</p>';
    } else {
        echo '<p class="error">✗ Testimonials not updated</p>';
    }
    
} else {
    echo '<p class="error">✗ defaults.php file not found</p>';
}

// Test 2: Check example.json
echo '<h2>Test 2: Example.json Content Check</h2>';
$example_file = dirname(__FILE__) . '/admin/views/example.json';

if (file_exists($example_file)) {
    echo '<p class="success">✓ example.json file exists</p>';
    
    $example_content = file_get_contents($example_file);
    $example_data = json_decode($example_content, true);
    
    if ($example_data && is_array($example_data) && count($example_data) >= 2) {
        echo '<p class="success">✓ example.json has correct structure</p>';
        
        // Check first example
        if (isset($example_data[0]['hero_title']) && 
            strpos($example_data[0]['hero_title'], 'Jasa Pembuatan Website Jakarta') !== false) {
            echo '<p class="success">✓ First example updated correctly (Jakarta)</p>';
        } else {
            echo '<p class="error">✗ First example not updated correctly</p>';
        }
        
        // Check second example
        if (isset($example_data[1]['hero_title']) && 
            strpos($example_data[1]['hero_title'], 'Jasa Pembuatan Website Surabaya') !== false) {
            echo '<p class="success">✓ Second example updated correctly (Surabaya)</p>';
        } else {
            echo '<p class="error">✗ Second example not updated correctly</p>';
        }
        
    } else {
        echo '<p class="error">✗ example.json has invalid structure</p>';
    }
    
} else {
    echo '<p class="error">✗ example.json file not found</p>';
}

// Test 3: Check admin.js AI prompt generator
echo '<h2>Test 3: Admin.js AI Prompt Generator Check</h2>';
$admin_js_file = dirname(__FILE__) . '/admin/js/admin.js';

if (file_exists($admin_js_file)) {
    echo '<p class="success">✓ admin.js file exists</p>';
    
    $admin_js_content = file_get_contents($admin_js_file);
    
    // Check if generatePrompt function has been updated
    if (strpos($admin_js_content, 'Professional Website Development Services') !== false) {
        echo '<p class="success">✓ AI prompt generator updated with website development context</p>';
    } else {
        echo '<p class="error">✗ AI prompt generator not updated</p>';
    }
    
    // Check if Yoast SEO title format is updated
    if (strpos($admin_js_content, 'Jasa Pembuatan Website') !== false) {
        echo '<p class="success">✓ Yoast SEO title format updated correctly</p>';
    } else {
        echo '<p class="error">✗ Yoast SEO title format not updated</p>';
    }
    
} else {
    echo '<p class="error">✗ admin.js file not found</p>';
}

// Test 4: Template Manager Integration Test
echo '<h2>Test 4: Template Manager Integration</h2>';

if (function_exists('rife_pg') && isset(rife_pg()->template_manager)) {
    echo '<p class="success">✓ Template Manager available</p>';
    
    $template_manager = rife_pg()->template_manager;
    
    // Test getting template defaults
    try {
        $defaults = $template_manager->get_template_defaults('seo-comprehensive');
        
        if ($defaults && isset($defaults['content']['hero']['title'])) {
            echo '<p class="success">✓ Template defaults loaded successfully</p>';
            echo '<p class="info">Hero title: ' . htmlspecialchars($defaults['content']['hero']['title']) . '</p>';
        } else {
            echo '<p class="error">✗ Template defaults not loaded correctly</p>';
        }
        
    } catch (Exception $e) {
        echo '<p class="error">✗ Exception loading template defaults: ' . $e->getMessage() . '</p>';
    }
    
} else {
    echo '<p class="error">✗ Template Manager not available</p>';
}

echo '<h2>Summary</h2>';
echo '<p class="info">Template changes test completed. Please check the results above and verify that:</p>';
echo '<ul>';
echo '<li>All files have been updated correctly</li>';
echo '<li>Content reflects the jasamurahweb.com branding</li>';
echo '<li>Pricing matches the demo-1.html structure</li>';
echo '<li>AI prompt generator focuses on website development services</li>';
echo '</ul>';

echo '<p class="info"><strong>Next Steps:</strong></p>';
echo '<ol>';
echo '<li>Go to wp-admin/admin.php?page=rife-pg-generator</li>';
echo '<li>Select "SEO Comprehensive" template</li>';
echo '<li>Click "Fill Dummy Content" to test</li>';
echo '<li>Test bulk generate with the updated example.json</li>';
echo '<li>Test AI prompt generator in Template Gallery</li>';
echo '</ol>';
?>
