/**
 * Style Customizer JavaScript for Rife PageGenerator
 * Handles live preview and style customization
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Style Customizer Object
    var StyleCustomizer = {
        
        // Current styles
        currentStyles: {},
        
        // Preview iframe
        previewFrame: null,
        
        // Initialize customizer
        init: function() {
            this.bindEvents();
            this.initializeColorPickers();
            this.initializeRangeSliders();
            this.loadDefaultStyles();
            this.setupLivePreview();
        },
        
        // Bind events
        bindEvents: function() {
            // Style control changes
            $('.rife-pg-style-customizer').on('change input', 'input, select', this.handleStyleChange.bind(this));
            
            // Reset styles
            $('#reset-styles').on('click', this.resetStyles.bind(this));
            
            // Preview styles
            $('#preview-styles').on('click', this.previewStyles.bind(this));
            
            // Import/Export styles
            $('#import-styles').on('click', this.importStyles.bind(this));
            $('#export-styles').on('click', this.exportStyles.bind(this));
            
            // Preset styles
            $('.style-preset').on('click', this.applyPreset.bind(this));
            
            // Section toggles
            $('.customizer-section .section-title').on('click', this.toggleSection.bind(this));
        },
        
        // Initialize color pickers
        initializeColorPickers: function() {
            if ($.fn.wpColorPicker) {
                $('.color-picker').wpColorPicker({
                    change: this.handleColorChange.bind(this),
                    clear: this.handleColorClear.bind(this)
                });
            }
        },
        
        // Initialize range sliders
        initializeRangeSliders: function() {
            $('input[type="range"]').each(function() {
                var slider = $(this);
                var valueDisplay = slider.siblings('.range-value');
                
                slider.on('input', function() {
                    var value = $(this).val();
                    var unit = $(this).data('unit') || 'px';
                    valueDisplay.text(value + unit);
                    
                    // Trigger style change
                    StyleCustomizer.handleStyleChange.call(this);
                });
                
                // Initialize display
                slider.trigger('input');
            });
        },
        
        // Load default styles
        loadDefaultStyles: function() {
            this.currentStyles = {
                primary_color: '#007cba',
                secondary_color: '#50575e',
                accent_color: '#00a32a',
                text_color: '#1e1e1e',
                background_color: '#ffffff',
                heading_font: 'Inter',
                body_font: 'Inter',
                font_size_base: '16',
                line_height: '1.6',
                section_padding: '80',
                container_width: '1200',
                border_radius: '8',
                button_style: 'rounded',
                shadow_style: 'subtle'
            };
            
            this.applyStylesToControls();
        },
        
        // Handle style changes
        handleStyleChange: function(e) {
            var control = $(e ? e.target : this);
            var name = control.attr('name') || control.attr('id');
            var value = control.val();
            
            if (name && value !== undefined) {
                this.currentStyles[name] = value;
                this.updateLivePreview();
                this.saveToLocalStorage();
            }
        },
        
        // Handle color picker changes
        handleColorChange: function(event, ui) {
            var control = $(event.target);
            var name = control.attr('name') || control.attr('id');
            var color = ui.color.toString();
            
            this.currentStyles[name] = color;
            this.updateLivePreview();
            this.saveToLocalStorage();
        },
        
        // Handle color picker clear
        handleColorClear: function(event) {
            var control = $(event.target);
            var name = control.attr('name') || control.attr('id');
            
            // Reset to default
            var defaultValue = control.data('default-color') || '#ffffff';
            this.currentStyles[name] = defaultValue;
            this.updateLivePreview();
            this.saveToLocalStorage();
        },
        
        // Setup live preview
        setupLivePreview: function() {
            // Create preview container if it doesn't exist
            if ($('#style-preview-container').length === 0) {
                var previewHtml = '<div id="style-preview-container" class="mt-4" style="display: none;">';
                previewHtml += '<h4>Live Preview</h4>';
                previewHtml += '<div class="preview-frame-container">';
                previewHtml += '<iframe id="style-preview-frame" width="100%" height="400" frameborder="0"></iframe>';
                previewHtml += '</div>';
                previewHtml += '</div>';
                
                $('.rife-pg-style-customizer').append(previewHtml);
            }
        },
        
        // Update live preview
        updateLivePreview: function() {
            // Debounce preview updates
            clearTimeout(this.previewTimeout);
            this.previewTimeout = setTimeout(function() {
                StyleCustomizer.generatePreviewCSS();
                StyleCustomizer.updatePreviewFrame();
            }, 300);
        },
        
        // Generate preview CSS
        generatePreviewCSS: function() {
            var css = ':root {\n';
            css += '  --primary-color: ' + this.currentStyles.primary_color + ';\n';
            css += '  --secondary-color: ' + this.currentStyles.secondary_color + ';\n';
            css += '  --accent-color: ' + this.currentStyles.accent_color + ';\n';
            css += '  --text-color: ' + this.currentStyles.text_color + ';\n';
            css += '  --background-color: ' + this.currentStyles.background_color + ';\n';
            css += '  --font-size-base: ' + this.currentStyles.font_size_base + 'px;\n';
            css += '  --line-height: ' + this.currentStyles.line_height + ';\n';
            css += '  --section-padding: ' + this.currentStyles.section_padding + 'px;\n';
            css += '  --container-width: ' + this.currentStyles.container_width + 'px;\n';
            css += '  --border-radius: ' + this.currentStyles.border_radius + 'px;\n';
            css += '}\n\n';
            
            // Typography
            css += 'body, p, div, span {\n';
            css += '  font-family: "' + this.currentStyles.body_font + '", sans-serif;\n';
            css += '  font-size: var(--font-size-base);\n';
            css += '  line-height: var(--line-height);\n';
            css += '  color: var(--text-color);\n';
            css += '}\n\n';
            
            css += 'h1, h2, h3, h4, h5, h6 {\n';
            css += '  font-family: "' + this.currentStyles.heading_font + '", sans-serif;\n';
            css += '}\n\n';
            
            // Layout
            css += '.container {\n';
            css += '  max-width: var(--container-width);\n';
            css += '}\n\n';
            
            css += '.section {\n';
            css += '  padding: var(--section-padding) 0;\n';
            css += '}\n\n';
            
            // Buttons
            var buttonRadius = this.getButtonRadius();
            css += '.btn, .button {\n';
            css += '  border-radius: ' + buttonRadius + ';\n';
            css += '  background-color: var(--primary-color);\n';
            css += '  border-color: var(--primary-color);\n';
            css += '}\n\n';
            
            css += '.btn:hover, .button:hover {\n';
            css += '  background-color: var(--secondary-color);\n';
            css += '  border-color: var(--secondary-color);\n';
            css += '}\n\n';
            
            // Shadows
            if (this.currentStyles.shadow_style !== 'none') {
                var shadow = this.getShadowStyle();
                css += '.card, .btn, .form-control {\n';
                css += '  box-shadow: ' + shadow + ';\n';
                css += '}\n\n';
            }
            
            this.previewCSS = css;
            return css;
        },
        
        // Get button radius based on style
        getButtonRadius: function() {
            switch (this.currentStyles.button_style) {
                case 'square':
                    return '0px';
                case 'pill':
                    return '50px';
                case 'rounded':
                default:
                    return this.currentStyles.border_radius + 'px';
            }
        },
        
        // Get shadow style
        getShadowStyle: function() {
            switch (this.currentStyles.shadow_style) {
                case 'subtle':
                    return '0 1px 3px rgba(0,0,0,0.1)';
                case 'medium':
                    return '0 4px 6px rgba(0,0,0,0.1)';
                case 'strong':
                    return '0 10px 25px rgba(0,0,0,0.15)';
                default:
                    return 'none';
            }
        },
        
        // Update preview frame
        updatePreviewFrame: function() {
            var frame = $('#style-preview-frame');
            if (frame.length === 0) return;
            
            // Create preview HTML
            var previewHtml = this.generatePreviewHTML();
            
            // Write to iframe
            var frameDoc = frame[0].contentDocument || frame[0].contentWindow.document;
            frameDoc.open();
            frameDoc.write(previewHtml);
            frameDoc.close();
        },
        
        // Generate preview HTML
        generatePreviewHTML: function() {
            var html = '<!DOCTYPE html><html><head>';
            html += '<meta charset="UTF-8">';
            html += '<meta name="viewport" content="width=device-width, initial-scale=1">';
            html += '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">';
            html += '<link href="https://fonts.googleapis.com/css2?family=' + this.currentStyles.heading_font.replace(' ', '+') + ':wght@300;400;500;600;700&display=swap" rel="stylesheet">';
            html += '<link href="https://fonts.googleapis.com/css2?family=' + this.currentStyles.body_font.replace(' ', '+') + ':wght@300;400;500;600;700&display=swap" rel="stylesheet">';
            html += '<style>' + this.previewCSS + '</style>';
            html += '</head><body>';
            
            // Sample content
            html += '<div class="container py-4">';
            html += '<div class="section">';
            html += '<h1>Sample Heading</h1>';
            html += '<p>This is a sample paragraph to show how your typography and colors will look.</p>';
            html += '<div class="row g-3">';
            html += '<div class="col-md-4">';
            html += '<div class="card p-3">';
            html += '<h4>Card Title</h4>';
            html += '<p>Sample card content with your custom styling.</p>';
            html += '<button class="btn btn-primary">Sample Button</button>';
            html += '</div>';
            html += '</div>';
            html += '<div class="col-md-4">';
            html += '<div class="card p-3">';
            html += '<h4>Another Card</h4>';
            html += '<p>More sample content to preview your design.</p>';
            html += '<button class="btn btn-secondary">Secondary Button</button>';
            html += '</div>';
            html += '</div>';
            html += '<div class="col-md-4">';
            html += '<div class="card p-3">';
            html += '<h4>Third Card</h4>';
            html += '<p>Final sample to see your complete style.</p>';
            html += '<button class="btn btn-outline-primary">Outline Button</button>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            
            html += '</body></html>';
            
            return html;
        },
        
        // Reset styles to default
        resetStyles: function(e) {
            e.preventDefault();
            
            if (confirm('Are you sure you want to reset all styles to default values?')) {
                this.loadDefaultStyles();
                this.updateLivePreview();
                this.clearLocalStorage();
            }
        },
        
        // Preview styles
        previewStyles: function(e) {
            e.preventDefault();
            
            var container = $('#style-preview-container');
            if (container.is(':visible')) {
                container.slideUp();
                $(e.target).text('Show Preview');
            } else {
                this.updateLivePreview();
                container.slideDown();
                $(e.target).text('Hide Preview');
            }
        },
        
        // Apply styles to controls
        applyStylesToControls: function() {
            var self = this;
            
            Object.keys(this.currentStyles).forEach(function(key) {
                var value = self.currentStyles[key];
                var control = $('[name="' + key + '"], #' + key);
                
                if (control.length) {
                    if (control.hasClass('color-picker')) {
                        control.wpColorPicker('color', value);
                    } else {
                        control.val(value);
                        if (control.attr('type') === 'range') {
                            control.trigger('input');
                        }
                    }
                }
            });
        },
        
        // Save to localStorage
        saveToLocalStorage: function() {
            localStorage.setItem('rife_pg_custom_styles', JSON.stringify(this.currentStyles));
        },
        
        // Load from localStorage
        loadFromLocalStorage: function() {
            var saved = localStorage.getItem('rife_pg_custom_styles');
            if (saved) {
                try {
                    this.currentStyles = JSON.parse(saved);
                    this.applyStylesToControls();
                    return true;
                } catch (e) {
                    console.log('Error loading saved styles:', e);
                }
            }
            return false;
        },
        
        // Clear localStorage
        clearLocalStorage: function() {
            localStorage.removeItem('rife_pg_custom_styles');
        },
        
        // Import styles
        importStyles: function(e) {
            e.preventDefault();
            
            var input = $('<input type="file" accept=".json">');
            input.on('change', function(e) {
                var file = e.target.files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            var styles = JSON.parse(e.target.result);
                            StyleCustomizer.currentStyles = styles;
                            StyleCustomizer.applyStylesToControls();
                            StyleCustomizer.updateLivePreview();
                            StyleCustomizer.saveToLocalStorage();
                            alert('Styles imported successfully!');
                        } catch (err) {
                            alert('Error importing styles: Invalid file format');
                        }
                    };
                    reader.readAsText(file);
                }
            });
            input.click();
        },
        
        // Export styles
        exportStyles: function(e) {
            e.preventDefault();
            
            var styles = JSON.stringify(this.currentStyles, null, 2);
            var blob = new Blob([styles], { type: 'application/json' });
            var url = URL.createObjectURL(blob);
            
            var a = $('<a>');
            a.attr('href', url);
            a.attr('download', 'rife-pg-styles.json');
            a[0].click();
            
            URL.revokeObjectURL(url);
        },
        
        // Apply preset styles
        applyPreset: function(e) {
            e.preventDefault();
            
            var preset = $(e.target).data('preset');
            var presets = this.getStylePresets();
            
            if (presets[preset]) {
                this.currentStyles = $.extend({}, this.currentStyles, presets[preset]);
                this.applyStylesToControls();
                this.updateLivePreview();
                this.saveToLocalStorage();
            }
        },
        
        // Get style presets
        getStylePresets: function() {
            return {
                'modern': {
                    primary_color: '#6366f1',
                    secondary_color: '#8b5cf6',
                    accent_color: '#06b6d4',
                    heading_font: 'Poppins',
                    body_font: 'Inter',
                    border_radius: '12',
                    button_style: 'rounded',
                    shadow_style: 'medium'
                },
                'classic': {
                    primary_color: '#2563eb',
                    secondary_color: '#1e40af',
                    accent_color: '#059669',
                    heading_font: 'Georgia',
                    body_font: 'Times New Roman',
                    border_radius: '4',
                    button_style: 'square',
                    shadow_style: 'subtle'
                },
                'vibrant': {
                    primary_color: '#ec4899',
                    secondary_color: '#f59e0b',
                    accent_color: '#10b981',
                    heading_font: 'Montserrat',
                    body_font: 'Open Sans',
                    border_radius: '20',
                    button_style: 'pill',
                    shadow_style: 'strong'
                }
            };
        },
        
        // Toggle section
        toggleSection: function(e) {
            var section = $(e.target).closest('.customizer-section');
            var content = section.find('.section-content');
            
            content.slideToggle();
            section.toggleClass('collapsed');
        },
        
        // Get current styles
        getCurrentStyles: function() {
            return this.currentStyles;
        }
    };
    
    // Initialize style customizer
    StyleCustomizer.init();
    
    // Try to load saved styles
    StyleCustomizer.loadFromLocalStorage();
    
    // Make StyleCustomizer available globally
    window.RifePgStyleCustomizer = StyleCustomizer;
});
