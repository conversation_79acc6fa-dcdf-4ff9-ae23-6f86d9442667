<?php
/**
 * Generated Pages Admin View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap rife-pg-admin">
    <h1 class="wp-heading-inline">
        <?php _e('Generated Pages', 'rife-pagegenerator'); ?>
        <span class="title-count"><?php echo count($generated_pages); ?></span>
    </h1>
    
    <a href="<?php echo admin_url('admin.php?page=rife-pg-generator'); ?>" class="page-title-action">
        <?php _e('Create New Page', 'rife-pagegenerator'); ?>
    </a>
    
    <p class="description">
        <?php _e('Manage all pages created with Rife PageGenerator. You can edit, duplicate, or delete generated pages from here.', 'rife-pagegenerator'); ?>
    </p>
    
    <?php if (empty($generated_pages)): ?>
        <div class="no-pages">
            <div class="no-pages-icon">
                <span class="dashicons dashicons-admin-page"></span>
            </div>
            <h3><?php _e('No Generated Pages Yet', 'rife-pagegenerator'); ?></h3>
            <p><?php _e('You haven\'t created any landing pages yet. Start by choosing a template and creating your first page.', 'rife-pagegenerator'); ?></p>
            <a href="<?php echo admin_url('admin.php?page=rife-pagegenerator'); ?>" class="button button-primary">
                <?php _e('Browse Templates', 'rife-pagegenerator'); ?>
            </a>
        </div>
    <?php else: ?>
        <div class="pages-list">
            <div class="tablenav top">
                <div class="alignleft actions">
                    <select name="action" id="bulk-action-selector-top">
                        <option value="-1"><?php _e('Bulk Actions', 'rife-pagegenerator'); ?></option>
                        <option value="delete"><?php _e('Delete', 'rife-pagegenerator'); ?></option>
                        <option value="publish"><?php _e('Publish', 'rife-pagegenerator'); ?></option>
                        <option value="draft"><?php _e('Move to Draft', 'rife-pagegenerator'); ?></option>
                    </select>
                    <input type="submit" id="doaction" class="button action" value="<?php _e('Apply', 'rife-pagegenerator'); ?>">
                </div>
                <div class="tablenav-pages">
                    <span class="displaying-num"><?php printf(_n('%s item', '%s items', count($generated_pages), 'rife-pagegenerator'), count($generated_pages)); ?></span>
                </div>
            </div>
            
            <table class="wp-list-table widefat fixed striped pages">
                <thead>
                    <tr>
                        <td id="cb" class="manage-column column-cb check-column">
                            <input id="cb-select-all-1" type="checkbox">
                        </td>
                        <th scope="col" class="manage-column column-title column-primary">
                            <?php _e('Title', 'rife-pagegenerator'); ?>
                        </th>
                        <th scope="col" class="manage-column column-template">
                            <?php _e('Template', 'rife-pagegenerator'); ?>
                        </th>
                        <th scope="col" class="manage-column column-status">
                            <?php _e('Status', 'rife-pagegenerator'); ?>
                        </th>
                        <th scope="col" class="manage-column column-date">
                            <?php _e('Date', 'rife-pagegenerator'); ?>
                        </th>
                        <th scope="col" class="manage-column column-actions">
                            <?php _e('Actions', 'rife-pagegenerator'); ?>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($generated_pages as $page_data): 
                        $page = get_post($page_data->page_id);
                        if (!$page) continue;
                        
                        $template_manager = rife_pg()->template_manager;
                        $template = $template_manager->get_template($page_data->template_id);
                        $template_name = $template ? $template['name'] : $page_data->template_id;
                        
                        $edit_url = get_edit_post_link($page->ID);
                        $view_url = get_permalink($page->ID);
                        $status = get_post_status($page->ID);
                        $status_label = $status === 'publish' ? __('Published', 'rife-pagegenerator') : ucfirst($status);
                    ?>
                        <tr>
                            <th scope="row" class="check-column">
                                <input id="cb-select-<?php echo $page->ID; ?>" type="checkbox" name="post[]" value="<?php echo $page->ID; ?>">
                            </th>
                            <td class="title column-title column-primary page-title">
                                <strong>
                                    <a class="row-title" href="<?php echo esc_url($edit_url); ?>">
                                        <?php echo esc_html($page->post_title); ?>
                                    </a>
                                    <?php if ($status !== 'publish'): ?>
                                        <span class="post-state"> — <?php echo esc_html($status_label); ?></span>
                                    <?php endif; ?>
                                </strong>
                                <div class="row-actions">
                                    <span class="edit">
                                        <a href="<?php echo esc_url($edit_url); ?>"><?php _e('Edit', 'rife-pagegenerator'); ?></a> |
                                    </span>
                                    <?php if ($status === 'publish'): ?>
                                        <span class="view">
                                            <a href="<?php echo esc_url($view_url); ?>" target="_blank"><?php _e('View', 'rife-pagegenerator'); ?></a> |
                                        </span>
                                    <?php endif; ?>
                                    <span class="regenerate">
                                        <a href="<?php echo admin_url('admin.php?page=rife-pg-generator&edit=' . $page->ID); ?>"><?php _e('Regenerate', 'rife-pagegenerator'); ?></a> |
                                    </span>
                                    <span class="duplicate">
                                        <a href="#" class="duplicate-page" data-page-id="<?php echo $page->ID; ?>"><?php _e('Duplicate', 'rife-pagegenerator'); ?></a> |
                                    </span>
                                    <span class="trash">
                                        <a href="#" class="delete-page" data-page-id="<?php echo $page->ID; ?>"><?php _e('Delete', 'rife-pagegenerator'); ?></a>
                                    </span>
                                </div>
                            </td>
                            <td class="template column-template">
                                <div class="template-info">
                                    <strong><?php echo esc_html($template_name); ?></strong>
                                    <?php if ($template): ?>
                                        <br><small><?php echo esc_html($template['category']); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="status column-status">
                                <span class="status-<?php echo esc_attr($status); ?>">
                                    <?php echo esc_html($status_label); ?>
                                </span>
                            </td>
                            <td class="date column-date">
                                <abbr title="<?php echo esc_attr($page_data->created_at); ?>">
                                    <?php echo date_i18n(get_option('date_format'), strtotime($page_data->created_at)); ?>
                                </abbr>
                                <br>
                                <small><?php echo date_i18n(get_option('time_format'), strtotime($page_data->created_at)); ?></small>
                            </td>
                            <td class="actions column-actions">
                                <div class="action-buttons">
                                    <?php if ($status === 'publish'): ?>
                                        <a href="<?php echo esc_url($view_url); ?>" class="button button-small" target="_blank">
                                            <span class="dashicons dashicons-external"></span>
                                            <?php _e('View', 'rife-pagegenerator'); ?>
                                        </a>
                                    <?php endif; ?>
                                    <a href="<?php echo esc_url($edit_url); ?>" class="button button-small">
                                        <span class="dashicons dashicons-edit"></span>
                                        <?php _e('Edit', 'rife-pagegenerator'); ?>
                                    </a>
                                    <a href="<?php echo admin_url('admin.php?page=rife-pg-generator&edit=' . $page->ID); ?>" class="button button-small">
                                        <span class="dashicons dashicons-update"></span>
                                        <?php _e('Regenerate', 'rife-pagegenerator'); ?>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <div class="tablenav bottom">
                <div class="alignleft actions">
                    <select name="action2" id="bulk-action-selector-bottom">
                        <option value="-1"><?php _e('Bulk Actions', 'rife-pagegenerator'); ?></option>
                        <option value="delete"><?php _e('Delete', 'rife-pagegenerator'); ?></option>
                        <option value="publish"><?php _e('Publish', 'rife-pagegenerator'); ?></option>
                        <option value="draft"><?php _e('Move to Draft', 'rife-pagegenerator'); ?></option>
                    </select>
                    <input type="submit" id="doaction2" class="button action" value="<?php _e('Apply', 'rife-pagegenerator'); ?>">
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.no-pages {
    text-align: center;
    padding: 60px 20px;
    color: #646970;
}

.no-pages-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.no-pages h3 {
    margin: 0 0 8px 0;
    color: #1d2327;
}

.template-info strong {
    color: #1d2327;
}

.template-info small {
    color: #646970;
    text-transform: capitalize;
}

.status-publish {
    color: #00a32a;
    font-weight: 500;
}

.status-draft {
    color: #dba617;
    font-weight: 500;
}

.status-private {
    color: #d63638;
    font-weight: 500;
}

.action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.action-buttons .button {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    font-size: 12px;
}

.action-buttons .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Delete page functionality
    $('.delete-page').on('click', function(e) {
        e.preventDefault();
        
        var pageId = $(this).data('page-id');
        var row = $(this).closest('tr');
        
        if (confirm('<?php _e('Are you sure you want to delete this page? This action cannot be undone.', 'rife-pagegenerator'); ?>')) {
            $.ajax({
                url: rifePgAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'rife_pg_delete_page',
                    page_id: pageId,
                    nonce: rifePgAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        row.fadeOut(function() {
                            $(this).remove();
                            updatePageCount();
                        });
                    } else {
                        alert('<?php _e('Failed to delete page', 'rife-pagegenerator'); ?>');
                    }
                },
                error: function() {
                    alert('<?php _e('An error occurred', 'rife-pagegenerator'); ?>');
                }
            });
        }
    });
    
    // Duplicate page functionality
    $('.duplicate-page').on('click', function(e) {
        e.preventDefault();
        
        var pageId = $(this).data('page-id');
        
        if (confirm('<?php _e('Create a duplicate of this page?', 'rife-pagegenerator'); ?>')) {
            $.ajax({
                url: rifePgAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'rife_pg_duplicate_page',
                    page_id: pageId,
                    nonce: rifePgAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('<?php _e('Failed to duplicate page', 'rife-pagegenerator'); ?>');
                    }
                },
                error: function() {
                    alert('<?php _e('An error occurred', 'rife-pagegenerator'); ?>');
                }
            });
        }
    });
    
    // Update page count
    function updatePageCount() {
        var count = $('tbody tr').length;
        $('.title-count').text(count);
        $('.displaying-num').text(count + ' <?php _e('items', 'rife-pagegenerator'); ?>');
        
        if (count === 0) {
            location.reload();
        }
    }
    
    // Select all checkbox
    $('#cb-select-all-1').on('change', function() {
        $('tbody input[type="checkbox"]').prop('checked', $(this).is(':checked'));
    });
});
</script>
