<?php
/**
 * Run All Tests for Rife PageGenerator Yoast SEO Integration
 * 
 * This script runs all available tests to ensure the complete integration
 * is working correctly.
 */

echo "=== Rife PageGenerator - Complete Test Suite ===\n";
echo "Running all tests to verify Yoast SEO integration...\n\n";

$start_time = microtime(true);
$test_results = [];

/**
 * Run a test file and capture results
 */
function run_test($test_file, $test_name) {
    global $test_results;
    
    echo "🔄 Running $test_name...\n";
    echo str_repeat("-", 50) . "\n";
    
    $test_start = microtime(true);
    
    // Capture output
    ob_start();
    $exit_code = 0;
    
    try {
        include $test_file;
    } catch (Exception $e) {
        echo "❌ Exception in $test_name: " . $e->getMessage() . "\n";
        $exit_code = 1;
    }
    
    $output = ob_get_clean();
    $test_end = microtime(true);
    $duration = round($test_end - $test_start, 2);
    
    // Analyze output for success/failure
    $success_count = substr_count($output, '✅');
    $error_count = substr_count($output, '❌');
    $warning_count = substr_count($output, '⚠️');
    
    $test_results[] = [
        'name' => $test_name,
        'duration' => $duration,
        'success_count' => $success_count,
        'error_count' => $error_count,
        'warning_count' => $warning_count,
        'exit_code' => $exit_code,
        'output' => $output
    ];
    
    echo $output;
    echo "\n✅ $test_name completed in {$duration}s\n";
    echo "   - Successes: $success_count\n";
    echo "   - Errors: $error_count\n";
    echo "   - Warnings: $warning_count\n\n";
    
    return $exit_code === 0 && $error_count === 0;
}

// Test 1: File Structure and Basic Checks
echo "📋 TEST 1: File Structure and Basic Environment\n";
$test1_passed = run_test(__DIR__ . '/simple-test.php', 'File Structure Test');

// Test 2: Image Generator
echo "📋 TEST 2: Image Generator Functionality\n";
$test2_passed = run_test(__DIR__ . '/test-image-standalone.php', 'Image Generator Test');

// Test 3: Yoast SEO Extraction
echo "📋 TEST 3: Yoast SEO Data Extraction\n";
$test3_passed = run_test(__DIR__ . '/test-yoast-extraction.php', 'Yoast SEO Extraction Test');

// Calculate overall results
$end_time = microtime(true);
$total_duration = round($end_time - $start_time, 2);

$total_successes = array_sum(array_column($test_results, 'success_count'));
$total_errors = array_sum(array_column($test_results, 'error_count'));
$total_warnings = array_sum(array_column($test_results, 'warning_count'));

$tests_passed = 0;
$tests_failed = 0;

foreach ($test_results as $result) {
    if ($result['exit_code'] === 0 && $result['error_count'] === 0) {
        $tests_passed++;
    } else {
        $tests_failed++;
    }
}

// Display final results
echo str_repeat("=", 60) . "\n";
echo "🏁 FINAL TEST RESULTS\n";
echo str_repeat("=", 60) . "\n";

foreach ($test_results as $result) {
    $status = ($result['exit_code'] === 0 && $result['error_count'] === 0) ? '✅ PASSED' : '❌ FAILED';
    echo sprintf("%-30s %s (%ss)\n", $result['name'], $status, $result['duration']);
}

echo "\n📊 SUMMARY:\n";
echo "- Tests Passed: $tests_passed\n";
echo "- Tests Failed: $tests_failed\n";
echo "- Total Successes: $total_successes\n";
echo "- Total Errors: $total_errors\n";
echo "- Total Warnings: $total_warnings\n";
echo "- Total Duration: {$total_duration}s\n";

if ($tests_failed === 0) {
    echo "\n🎉 ALL TESTS PASSED!\n";
    echo "✅ Rife PageGenerator Yoast SEO Integration is READY FOR PRODUCTION!\n\n";
    
    echo "🚀 INTEGRATION STATUS: COMPLETE\n";
    echo "✅ Image Generator: Working\n";
    echo "✅ Yoast SEO Extraction: Working\n";
    echo "✅ File Structure: Complete\n";
    echo "✅ Error Handling: Robust\n";
    echo "✅ Validation: Comprehensive\n\n";
    
    echo "📋 NEXT STEPS:\n";
    echo "1. Deploy to WordPress environment\n";
    echo "2. Test with real Yoast SEO plugin\n";
    echo "3. Run bulk generation with sample JSON\n";
    echo "4. Verify generated pages have SEO metadata\n";
    echo "5. Check featured images are created and set\n\n";
    
    exit(0);
} else {
    echo "\n❌ SOME TESTS FAILED!\n";
    echo "Please review the failed tests above before deploying to production.\n\n";
    
    echo "🔍 FAILED TESTS:\n";
    foreach ($test_results as $result) {
        if ($result['exit_code'] !== 0 || $result['error_count'] > 0) {
            echo "- " . $result['name'] . " (Errors: " . $result['error_count'] . ")\n";
        }
    }
    
    echo "\n📋 TROUBLESHOOTING:\n";
    echo "1. Check error messages above\n";
    echo "2. Ensure all required files are present\n";
    echo "3. Verify PHP extensions (GD, JSON)\n";
    echo "4. Check file permissions\n";
    echo "5. Review TROUBLESHOOTING.md for detailed help\n\n";
    
    exit(1);
}
?>
