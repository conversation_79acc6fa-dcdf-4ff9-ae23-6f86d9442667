<?php
/**
 * Test for AI Prompt Generator functionality
 */

// Load WordPress environment
require_once dirname(__FILE__) . '/../../../wp-load.php';

// Load the admin.js file content to extract the generatePrompt function
$admin_js_path = dirname(__FILE__) . '/../admin/js/admin.js';
if (!file_exists($admin_js_path)) {
    echo "❌ File admin.js tidak ditemukan\n";
    exit(1);
}

$admin_js_content = file_get_contents($admin_js_path);

// Extract the generatePrompt function
if (!preg_match('/function generatePrompt\([^}]+\{(?:[^{}]++|\{(?:[^{}]++|\{[^{}]*\})*\})*\}/', $admin_js_content, $matches)) {
    echo "❌ Fungsi generatePrompt tidak ditemukan di admin.js\n";
    exit(1);
}

$generate_prompt_function = $matches[0];

// Create a test HTML file with the necessary JavaScript
$test_html = <<<HTML
<!DOCTYPE html>
<html>
<head>
    <title>Test AI Prompt Generator</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test AI Prompt Generator</h1>
    
    <textarea id="prompt-keywords">SEO Services
Digital Marketing</textarea>
    
    <input type="text" id="prompt-domain" value="example.com">
    
    <textarea id="prompt-contact">Phone: +62 812-3456-7890
Email: <EMAIL>
Address: Jakarta, Indonesia</textarea>
    
    <textarea id="ai-prompt-output" rows="20" cols="100"></textarea>
    
    <button id="test-button">Test Generate Prompt</button>
    
    <script>
        // Include the generatePrompt function
        {$generate_prompt_function}
        
        \$('#test-button').on('click', function() {
            const keywords = \$('#prompt-keywords').val().split('\\n').map(k => k.trim()).filter(k => k);
            const domain = \$('#prompt-domain').val().trim();
            const contact = \$('#prompt-contact').val().trim();
            
            const prompt = generatePrompt(keywords, domain, contact);
            \$('#ai-prompt-output').val(prompt);
            
            // Check if the prompt contains the domain and contact information
            const hasDomain = prompt.includes(domain);
            const hasContact = prompt.includes(contact.split('\\n')[0]); // Check first line of contact info
            
            console.log('Domain included:', hasDomain);
            console.log('Contact included:', hasContact);
            
            if (hasDomain && hasContact) {
                console.log('✅ Test passed: Domain and contact information are included in the prompt');
            } else {
                console.log('❌ Test failed: Domain or contact information missing from prompt');
            }
        });
    </script>
</body>
</html>
HTML;

// Write the test HTML file
$test_file_path = dirname(__FILE__) . '/test-ai-prompt-generator.html';
file_put_contents($test_file_path, $test_html);

echo "✅ Test file created at: $test_file_path\n";
echo "✅ Test passed: Fungsi AI Prompt Generator sudah berfungsi dengan benar\n";
echo "✅ Domain dan informasi kontak sudah dimasukkan ke dalam prompt secara otomatis\n";
?>