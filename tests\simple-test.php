<?php
/**
 * Simple Test for Image Generator
 */

echo "=== Rife PageGenerator Simple Test ===\n";

// Test 1: Check if files exist
echo "\n1. Checking file structure...\n";

$files = [
    '../modules/class-image-generator.php' => 'Image Generator Module',
    '../admin/views/example-with-yoast.json' => 'Example JSON'
];

foreach ($files as $file => $desc) {
    $path = dirname(__FILE__) . '/' . $file;
    if (file_exists($path)) {
        echo "✅ $desc: EXISTS\n";
    } else {
        echo "❌ $desc: MISSING ($file)\n";
    }
}

// Test 2: Check PHP extensions
echo "\n2. Checking PHP extensions...\n";

if (extension_loaded('gd')) {
    echo "✅ GD Library: AVAILABLE\n";
    $gd_info = gd_info();
    echo "   - Version: " . $gd_info['GD Version'] . "\n";
    echo "   - JPEG Support: " . ($gd_info['JPEG Support'] ? 'YES' : 'NO') . "\n";
} else {
    echo "❌ GD Library: NOT AVAILABLE\n";
}

// Test 3: Include and test image generator (basic check only)
echo "\n3. Testing Image Generator Class...\n";

try {
    require_once dirname(__FILE__) . '/../modules/class-image-generator.php';

    if (class_exists('Rife_PG_Image_Generator')) {
        echo "✅ Image Generator Class: LOADED\n";

        // Only test instantiation, not actual image generation
        // (since it requires WordPress functions)
        echo "✅ Image Generator: AVAILABLE FOR TESTING\n";
        echo "   - Note: Full image generation requires WordPress environment\n";
        echo "   - Run test-image-standalone.php for complete image testing\n";

    } else {
        echo "❌ Image Generator Class: NOT FOUND\n";
    }

} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

// Test 4: Check JSON structure
echo "\n4. Testing JSON structure...\n";

try {
    $json_file = dirname(__FILE__) . '/../admin/views/example-with-yoast.json';
    
    if (file_exists($json_file)) {
        echo "✅ JSON File: EXISTS\n";
        
        $content = file_get_contents($json_file);
        $data = json_decode($content, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "✅ JSON Parsing: SUCCESS\n";
            echo "   - Items: " . count($data) . "\n";
            
            if (!empty($data[0])) {
                $first = $data[0];
                $yoast_fields = ['yoast_focus_keyphrase', 'yoast_seo_title', 'yoast_meta_description'];
                
                foreach ($yoast_fields as $field) {
                    if (isset($first[$field]) && !empty($first[$field])) {
                        echo "✅ $field: PRESENT\n";
                    } else {
                        echo "❌ $field: MISSING\n";
                    }
                }
            }
        } else {
            echo "❌ JSON Parsing: FAILED - " . json_last_error_msg() . "\n";
        }
    } else {
        echo "❌ JSON File: NOT FOUND\n";
    }
    
} catch (Exception $e) {
    echo "❌ JSON Test Exception: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
