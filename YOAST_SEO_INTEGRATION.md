# Yoast SEO Integration for Rife PageGenerator

## Overview
Rife PageGenerator now supports automatic Yoast SEO integration for bulk generated pages. When using the "Bulk Generate" feature, you can include Yoast SEO data in your JSON file, and the plugin will automatically populate the SEO fields for each generated page.

## Supported Yoast SEO Fields

The following Yoast SEO fields are supported in the JSON file:

1. **yoast_focus_keyphrase** - The main keyword you want to rank for
2. **yoast_seo_title** - Custom SEO title for the page
3. **yoast_meta_description** - Meta description for search engines

## JSON Structure with Yoast SEO

Here's an example of how to structure your JSON file to include Yoast SEO data:

```json
[
  {
    "hero_title": "Jasa SEO Jakarta Profesional",
    "hero_subtitle": "Kami adalah agensi SEO terpercaya di Jakarta",
    "hero_cta_text": "Konsultasi Gratis",
    "hero_cta_link": "https://example.com/konsultasi",
    "benefit_1_title": "SEO On-Page Optimal",
    "benefit_1_desc": "Optimasi konten dan struktur website",
    "contact_email": "<EMAIL>",
    "yoast_focus_keyphrase": "jasa seo jakarta",
    "yoast_seo_title": "Jasa SEO Jakarta Profesional | Tingkatkan Ranking Website #1",
    "yoast_meta_description": "Dapatkan jasa SEO Jakarta terbaik. Kami telah membantu 500+ bisnis meningkatkan ranking Google. Konsultasi gratis, garansi hasil."
  }
]
```

## How It Works

1. **Upload JSON File**: Upload your JSON file through the Bulk Generate interface
2. **Data Processing**: The plugin automatically detects Yoast SEO fields in your JSON
3. **Page Generation**: Pages are created with all content and design settings
4. **SEO Metadata**: Yoast SEO metadata is automatically added to each page

## Yoast SEO Metadata Fields

When you include Yoast SEO data in your JSON, the following WordPress post meta fields are automatically set:

- `_yoast_wpseo_focuskw` - Focus keyphrase
- `_yoast_wpseo_focuskw_text_input` - Focus keyphrase (text input version)
- `_yoast_wpseo_title` - SEO title
- `_yoast_wpseo_metadesc` - Meta description
- `_yoast_wpseo_meta-robots-noindex` - Set to '0' (allow indexing)
- `_yoast_wpseo_meta-robots-nofollow` - Set to '0' (allow following)
- `_yoast_wpseo_content_score` - Content optimization score

## Example Files

Two example JSON files are provided:

1. **example.json** - Basic structure without Yoast SEO
2. **example-with-yoast.json** - Complete structure with Yoast SEO fields

## Benefits

- **Time Saving**: No need to manually set SEO data for each page
- **Consistency**: Ensure all pages have proper SEO optimization
- **Bulk Optimization**: Optimize hundreds of pages at once
- **SEO Ready**: Pages are immediately ready for search engine indexing

## Best Practices

1. **Focus Keyphrase**: Use specific, relevant keywords for each page
2. **SEO Title**: Keep under 60 characters for optimal display
3. **Meta Description**: Keep under 160 characters and include your focus keyphrase
4. **Unique Content**: Ensure each page has unique SEO data
5. **Keyword Research**: Do proper keyword research before creating bulk pages

## Troubleshooting

### Yoast SEO data not appearing?
- Ensure Yoast SEO plugin is installed and activated
- Check that your JSON includes the correct field names
- Verify the page was successfully generated

### SEO fields empty after generation?
- Check JSON syntax for errors
- Ensure field values are properly formatted
- Look for PHP error logs if issues persist

## Requirements

- Yoast SEO plugin must be installed and activated
- Rife PageGenerator plugin version 1.0.1 or higher
- WordPress 5.0 or higher

## Support

For support with Yoast SEO integration, please:
1. Check this documentation first
2. Review the example JSON files
3. Contact support if issues persist