<?php
/**
 * Settings Admin View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$template_manager = rife_pg()->template_manager;
$available_templates = $template_manager->get_available_templates();
$style_customizer = new Rife_PG_Style_Customizer();
$available_fonts = $style_customizer->get_available_fonts();
?>

<div class="wrap rife-pg-admin">
    <h1><?php _e('Rife PageGenerator Settings', 'rife-pagegenerator'); ?></h1>
    
    <p class="description">
        <?php _e('Configure default settings for your landing page generator.', 'rife-pagegenerator'); ?>
    </p>
    
    <form method="post" action="">
        <?php wp_nonce_field('rife_pg_settings'); ?>
        
        <div class="settings-container">
            <!-- General Settings -->
            <div class="settings-section">
                <h2><?php _e('General Settings', 'rife-pagegenerator'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="default_template"><?php _e('Default Template', 'rife-pagegenerator'); ?></label>
                        </th>
                        <td>
                            <select id="default_template" name="default_template">
                                <option value=""><?php _e('No default template', 'rife-pagegenerator'); ?></option>
                                <?php foreach ($available_templates as $template): ?>
                                    <option value="<?php echo esc_attr($template['id']); ?>" 
                                            <?php selected($options['default_template'] ?? '', $template['id']); ?>>
                                        <?php echo esc_html($template['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description">
                                <?php _e('Select a default template that will be pre-selected in the page generator.', 'rife-pagegenerator'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Page Generation', 'rife-pagegenerator'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="checkbox" name="enable_preview" value="1" 
                                           <?php checked($options['enable_preview'] ?? true); ?>>
                                    <?php _e('Enable template preview', 'rife-pagegenerator'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Allow users to preview templates before using them.', 'rife-pagegenerator'); ?>
                                </p>
                                
                                <label>
                                    <input type="checkbox" name="auto_publish" value="1" 
                                           <?php checked($options['auto_publish'] ?? false); ?>>
                                    <?php _e('Auto-publish generated pages', 'rife-pagegenerator'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Automatically publish pages when generated (otherwise they will be saved as drafts).', 'rife-pagegenerator'); ?>
                                </p>
                            </fieldset>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Default Colors -->
            <div class="settings-section">
                <h2><?php _e('Default Colors', 'rife-pagegenerator'); ?></h2>
                <p class="description">
                    <?php _e('Set default colors that will be pre-filled in the style customizer.', 'rife-pagegenerator'); ?>
                </p>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="primary_color"><?php _e('Primary Color', 'rife-pagegenerator'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="primary_color" name="primary_color" 
                                   value="<?php echo esc_attr($options['default_colors']['primary'] ?? '#007cba'); ?>" 
                                   class="color-picker" data-default-color="#007cba">
                            <p class="description">
                                <?php _e('Main brand color used for buttons, links, and accents.', 'rife-pagegenerator'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="secondary_color"><?php _e('Secondary Color', 'rife-pagegenerator'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="secondary_color" name="secondary_color" 
                                   value="<?php echo esc_attr($options['default_colors']['secondary'] ?? '#50575e'); ?>" 
                                   class="color-picker" data-default-color="#50575e">
                            <p class="description">
                                <?php _e('Secondary color used for text and subtle elements.', 'rife-pagegenerator'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Default Typography -->
            <div class="settings-section">
                <h2><?php _e('Default Typography', 'rife-pagegenerator'); ?></h2>
                <p class="description">
                    <?php _e('Set default fonts that will be pre-selected in the style customizer.', 'rife-pagegenerator'); ?>
                </p>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="heading_font"><?php _e('Heading Font', 'rife-pagegenerator'); ?></label>
                        </th>
                        <td>
                            <select id="heading_font" name="heading_font">
                                <?php foreach ($available_fonts as $font_value => $font_name): ?>
                                    <option value="<?php echo esc_attr($font_value); ?>" 
                                            <?php selected($options['default_fonts']['heading'] ?? 'Inter', $font_value); ?>>
                                        <?php echo esc_html($font_name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description">
                                <?php _e('Font family for headings (H1, H2, H3, etc.).', 'rife-pagegenerator'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="body_font"><?php _e('Body Font', 'rife-pagegenerator'); ?></label>
                        </th>
                        <td>
                            <select id="body_font" name="body_font">
                                <?php foreach ($available_fonts as $font_value => $font_name): ?>
                                    <option value="<?php echo esc_attr($font_value); ?>" 
                                            <?php selected($options['default_fonts']['body'] ?? 'Inter', $font_value); ?>>
                                        <?php echo esc_html($font_name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description">
                                <?php _e('Font family for body text and paragraphs.', 'rife-pagegenerator'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Advanced Settings -->
            <div class="settings-section">
                <h2><?php _e('Advanced Settings', 'rife-pagegenerator'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="custom_css"><?php _e('Custom CSS', 'rife-pagegenerator'); ?></label>
                        </th>
                        <td>
                            <textarea id="custom_css" name="custom_css" rows="10" cols="50" class="large-text code"><?php echo esc_textarea($options['custom_css'] ?? ''); ?></textarea>
                            <p class="description">
                                <?php _e('Add custom CSS that will be applied to all generated pages.', 'rife-pagegenerator'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="google_fonts_api_key"><?php _e('Google Fonts API Key', 'rife-pagegenerator'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="google_fonts_api_key" name="google_fonts_api_key" 
                                   value="<?php echo esc_attr($options['google_fonts_api_key'] ?? ''); ?>" 
                                   class="regular-text">
                            <p class="description">
                                <?php _e('Optional: Add your Google Fonts API key to access more font options.', 'rife-pagegenerator'); ?>
                                <a href="https://developers.google.com/fonts/docs/developer_api" target="_blank">
                                    <?php _e('Get API Key', 'rife-pagegenerator'); ?>
                                </a>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Performance', 'rife-pagegenerator'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="checkbox" name="minify_css" value="1" 
                                           <?php checked($options['minify_css'] ?? false); ?>>
                                    <?php _e('Minify generated CSS', 'rife-pagegenerator'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Compress CSS output for better performance.', 'rife-pagegenerator'); ?>
                                </p>
                                
                                <label>
                                    <input type="checkbox" name="cache_templates" value="1" 
                                           <?php checked($options['cache_templates'] ?? true); ?>>
                                    <?php _e('Cache template output', 'rife-pagegenerator'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Cache generated templates for better performance.', 'rife-pagegenerator'); ?>
                                </p>
                            </fieldset>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Plugin Information -->
            <div class="settings-section">
                <h2><?php _e('Plugin Information', 'rife-pagegenerator'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Version', 'rife-pagegenerator'); ?></th>
                        <td>
                            <strong><?php echo RIFE_PG_VERSION; ?></strong>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Database Version', 'rife-pagegenerator'); ?></th>
                        <td>
                            <?php echo get_option('rife_pg_db_version', '1.0'); ?>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Generated Pages', 'rife-pagegenerator'); ?></th>
                        <td>
                            <?php
                            global $wpdb;
                            $table_name = $wpdb->prefix . 'rife_pg_pages';
                            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
                            echo intval($count);
                            ?>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Available Templates', 'rife-pagegenerator'); ?></th>
                        <td>
                            <?php echo count($available_templates); ?>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Reset Section -->
            <div class="settings-section">
                <h2><?php _e('Reset & Maintenance', 'rife-pagegenerator'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Reset Settings', 'rife-pagegenerator'); ?></th>
                        <td>
                            <button type="button" class="button button-secondary" id="reset-settings">
                                <?php _e('Reset to Defaults', 'rife-pagegenerator'); ?>
                            </button>
                            <p class="description">
                                <?php _e('Reset all settings to their default values.', 'rife-pagegenerator'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Clear Cache', 'rife-pagegenerator'); ?></th>
                        <td>
                            <button type="button" class="button button-secondary" id="clear-cache">
                                <?php _e('Clear Template Cache', 'rife-pagegenerator'); ?>
                            </button>
                            <p class="description">
                                <?php _e('Clear all cached template data.', 'rife-pagegenerator'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <?php submit_button(__('Save Settings', 'rife-pagegenerator')); ?>
    </form>
</div>

<style>
.settings-container {
    max-width: 1000px;
}

.settings-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    margin-bottom: 20px;
    padding: 0;
}

.settings-section h2 {
    margin: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #c3c4c7;
    background: #f6f7f7;
    font-size: 16px;
}

.settings-section .form-table {
    margin: 0;
}

.settings-section .form-table th,
.settings-section .form-table td {
    padding: 20px;
}

.color-picker {
    width: 60px !important;
    height: 32px !important;
    padding: 2px !important;
    border-radius: 4px !important;
}

.large-text {
    width: 100%;
    max-width: 500px;
}

.code {
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Initialize color pickers
    if ($.fn.wpColorPicker) {
        $('.color-picker').wpColorPicker();
    }
    
    // Reset settings
    $('#reset-settings').on('click', function() {
        if (confirm('<?php _e('Are you sure you want to reset all settings to defaults? This cannot be undone.', 'rife-pagegenerator'); ?>')) {
            $.ajax({
                url: rifePgAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'rife_pg_reset_settings',
                    nonce: rifePgAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('<?php _e('Failed to reset settings', 'rife-pagegenerator'); ?>');
                    }
                }
            });
        }
    });
    
    // Clear cache
    $('#clear-cache').on('click', function() {
        $.ajax({
            url: rifePgAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'rife_pg_clear_cache',
                nonce: rifePgAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    alert('<?php _e('Cache cleared successfully', 'rife-pagegenerator'); ?>');
                } else {
                    alert('<?php _e('Failed to clear cache', 'rife-pagegenerator'); ?>');
                }
            }
        });
    });
});
</script>
